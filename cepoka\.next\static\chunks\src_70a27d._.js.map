{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/Hero.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Image from \"next/image\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport Link from \"next/link\";\r\nimport { useRef, useState, useEffect, useCallback } from \"react\";\r\nimport { useActiveLink } from \"../context/ActiveLinkContext\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { Search, ArrowRight, ChevronDown } from 'lucide-react';\r\n\r\nconst Hero = () => {\r\n    const { activeLink, setActiveLink } = useActiveLink();\r\n    const [menuOpen, setMenuOpen] = useState(false);\r\n\r\n    // Function to handle menu toggle\r\n    const handleMenuToggle = (isOpen: boolean) => {\r\n        setMenuOpen(isOpen);\r\n    };\r\n    const router = useRouter();\r\n    const [searchQuery, setSearchQuery] = useState(\"\");\r\n    const [showSearch, setShowSearch] = useState(false);\r\n    const [isMounted, setIsMounted] = useState(false);\r\n    const dropIconRef = useRef<HTMLDivElement>(null);\r\n    const actionsRef = useRef<HTMLDivElement>(null);\r\n    const [isOverlapping, setIsOverlapping] = useState(false);\r\n    const [currentModelIndex, setCurrentModelIndex] = useState(0);\r\n\r\n    // Array of model images\r\n    const modelImages = [\r\n        \"hero-graphis/hero1.png\",\r\n        \"hero-graphis/facemask model5.png\",\r\n        \"images/chair2.png\"\r\n        // Add more model image paths as needed\r\n    ];\r\n\r\n    // Image rotation effect\r\n    useEffect(() => {\r\n        if (!isMounted) return;\r\n\r\n        const interval = setInterval(() => {\r\n            setCurrentModelIndex((prevIndex) =>\r\n                prevIndex === modelImages.length - 1 ? 0 : prevIndex + 1\r\n            );\r\n        }, 4000); // Changed to 4 seconds for each image display\r\n\r\n        return () => clearInterval(interval);\r\n    }, [isMounted, modelImages.length]);\r\n\r\n    useEffect(() => {\r\n        setIsMounted(true);\r\n    }, []);\r\n\r\n    const scrollToSection = (sectionId: string) => {\r\n        if (!isMounted || typeof window === 'undefined') return;\r\n        const element = document.getElementById(sectionId);\r\n        if (element) {\r\n            element.scrollIntoView({ behavior: 'smooth' });\r\n        }\r\n    };\r\n\r\n    const handleNavClick = (link: string) => {\r\n        setActiveLink(link);\r\n        setMenuOpen(false);\r\n\r\n        if (!isMounted || typeof window === 'undefined') return;\r\n\r\n        switch (link.toLowerCase()) {\r\n            case 'contact':\r\n                scrollToSection('section7');\r\n                break;\r\n            case 'about':\r\n                scrollToSection('section6');\r\n                break;\r\n            case 'home':\r\n                window?.scrollTo({ top: 0, behavior: 'smooth' });\r\n                break;\r\n            case 'shop':\r\n                router.push('/shop');\r\n                break;\r\n        }\r\n    };\r\n\r\n    const handleSearch = (e?: React.FormEvent) => {\r\n        e?.preventDefault();\r\n        if (searchQuery.trim()) {\r\n            router.push(`/shop?search=${encodeURIComponent(searchQuery.trim())}`);\r\n        }\r\n        setShowSearch(false);\r\n\r\n        setSearchQuery(\"\");\r\n    };\r\n\r\n    const checkOverlap = useCallback(() => {\r\n        if (!isMounted || typeof window === 'undefined') return;\r\n\r\n        const dropIcon = dropIconRef.current;\r\n        const actions = actionsRef.current;\r\n\r\n        if (dropIcon && actions && window.innerWidth < 768) { // Only check on mobile\r\n            const dropRect = dropIcon.getBoundingClientRect();\r\n            const actionsRect = actions.getBoundingClientRect();\r\n\r\n            // Check if the drop icon overlaps with the actions section\r\n            const isOverlapping = !(\r\n                dropRect.top > actionsRect.bottom ||\r\n                dropRect.bottom < actionsRect.top\r\n            );\r\n\r\n            setIsOverlapping(isOverlapping);\r\n        }\r\n    }, [isMounted]);\r\n\r\n    useEffect(() => {\r\n        if (!isMounted || typeof window === 'undefined') return;\r\n\r\n        checkOverlap();\r\n        window.addEventListener('resize', checkOverlap);\r\n        window.addEventListener('scroll', checkOverlap);\r\n\r\n        return () => {\r\n            window.removeEventListener('resize', checkOverlap);\r\n            window.removeEventListener('scroll', checkOverlap);\r\n        };\r\n    }, [checkOverlap, isMounted]);\r\n\r\n    // framer motion variants --------\r\n    const heroTextVariants = {\r\n        hidden: { opacity: 0, x: -100 },\r\n        visible: {\r\n            opacity: 1,\r\n            x: 0,\r\n            transition: { duration: 1, ease: \"easeOut\" },\r\n        },\r\n    };\r\n\r\n    const highlightVariants = {\r\n        hidden: { opacity: 0, scale: 0.8 },\r\n        visible: {\r\n            opacity: 1,\r\n            scale: 1,\r\n            transition: {\r\n                duration: 1,\r\n                ease: \"easeOut\",\r\n                delay: 0.3, // Delay for smoother stagger\r\n            },\r\n        },\r\n    };\r\n\r\n    const modelImageVariants = {\r\n        hidden: { y: 50, opacity: 0 },\r\n        visible: {\r\n            y: 0,\r\n            opacity: 1,\r\n            transition: { duration: 1, ease: \"easeOut\" },\r\n        },\r\n    };\r\n\r\n    const cardVariants = {\r\n        hidden: { y: 20, opacity: 0 },\r\n        visible: {\r\n            y: 0,\r\n            opacity: 1,\r\n            transition: { duration: 1, ease: \"easeOut\", delay: 0.6 },\r\n        },\r\n    };\r\n\r\n    // Mobile menu variants are now defined inline\r\n\r\n    const menuItemVariants = {\r\n        closed: { opacity: 0, y: -20 },\r\n        open: (i: number) => ({\r\n            opacity: 1,\r\n            y: 0,\r\n            transition: {\r\n                delay: i * 0.1,\r\n                duration: 0.4,\r\n                ease: [0.4, 0, 0.2, 1],\r\n            }\r\n        })\r\n    };\r\n\r\n    // Animation variants for the model image\r\n    const slideUpVariants = {\r\n        enter: {\r\n            // y: 100,\r\n            opacity: 0\r\n        },\r\n        center: {\r\n            y: 0,\r\n            opacity: 1,\r\n            transition: {\r\n                y: { duration: 1.5, ease: [0.16, 1, 0.3, 1] },\r\n                opacity: { duration: 1.5, ease: [0.16, 1, 0.3, 1] }\r\n            }\r\n        },\r\n        exit: {\r\n            // y: -100,\r\n            opacity: 0,\r\n            transition: {\r\n                y: { duration: 1.5, ease: [0.16, 1, 0.3, 1] },\r\n                opacity: { duration: 1.5, ease: [0.16, 1, 0.3, 1] }\r\n            }\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"hero bg-[#ffffff] h-screen w-screen flex flex-col justify-center items-center relative\">\r\n            <div className=\"absolute bottom-0 left-0 w-full h-24 bg-gradient-to-t from-white to-transparent z-20\"></div>\r\n\r\n            {menuOpen && (\r\n                <motion.div\r\n                    initial={{ opacity: 0 }}\r\n                    animate={{ opacity: 1 }}\r\n                    exit={{ opacity: 0 }}\r\n                    transition={{ duration: 0.3 }}\r\n                    className=\"menu-overlay fixed inset-0 bg-black/50 backdrop-blur-sm z-[90]\"\r\n                    onClick={() => handleMenuToggle(false)}\r\n                />\r\n            )}\r\n\r\n            <div className=\"container mx-auto max-w-[1536px] flex flex-col md:flex-row justify-around items-center px-8 md:px-20 lg:px-40 pb-8 pt-0 md:pt-20\">\r\n                <div className={`logo md:mb-0 flex pt-14 justify-between w-full md:w-auto items-center relative z-[200] ${menuOpen ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}>\r\n                    <Link href={\"/\"} className=\"relative z-[200] bg-transparent p-1 rounded-full\">\r\n                        <Image\r\n                            className=\"w-10 h-auto md:w-30\"\r\n                            width={70}\r\n                            height={70}\r\n                            priority\r\n                            alt=\"Dfugo logo\"\r\n                            src=\"/logo.png\"\r\n                        />\r\n                    </Link>\r\n\r\n                    {/* Nav-style Hamburger Button */}\r\n                    {!menuOpen && (\r\n                        <button\r\n                            id=\"hero-hamburger-button\"\r\n                            className=\"text-black md:hidden focus:outline-none z-[120] p-2\"\r\n                            onClick={(e) => {\r\n                                e.stopPropagation();\r\n                                handleMenuToggle(true);\r\n                            }}\r\n                        >\r\n                            <svg\r\n                                className=\"w-6 h-6\"\r\n                                fill=\"none\"\r\n                                stroke=\"currentColor\"\r\n                                viewBox=\"0 0 24 24\"\r\n                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                            >\r\n                                <path\r\n                                    strokeLinecap=\"round\"\r\n                                    strokeLinejoin=\"round\"\r\n                                    strokeWidth=\"2\"\r\n                                    d=\"M4 6h16M4 12h16m-7 6h7\"\r\n                                />\r\n                            </svg>\r\n                        </button>\r\n                    )}\r\n                </div>\r\n\r\n                {/* Updated Mobile Menu with centered items and dots */}\r\n                <motion.div\r\n                    id=\"hero-mobile-menu-container\"\r\n                    initial=\"closed\"\r\n                    animate={menuOpen ? \"open\" : \"closed\"}\r\n                    variants={{\r\n                        open: {\r\n                            opacity: 1,\r\n                            y: 0,\r\n                            transition: {\r\n                                duration: 0.3,\r\n                                ease: [0.4, 0, 0.2, 1],\r\n                                staggerChildren: 0.1\r\n                            }\r\n                        },\r\n                        closed: {\r\n                            opacity: 0,\r\n                            y: \"-100%\",\r\n                            transition: {\r\n                                duration: 0.3,\r\n                                ease: [0.4, 0, 0.2, 1],\r\n                                staggerChildren: 0.05,\r\n                                staggerDirection: -1\r\n                            }\r\n                        }\r\n                    }}\r\n                    className=\"fixed top-0 left-0 w-full bg-[#11111180] backdrop-blur-[12px] z-[150] md:hidden h-full\"\r\n                    onClick={(e) => {\r\n                        e.stopPropagation();\r\n                        handleMenuToggle(false);\r\n                    }}\r\n                >\r\n                    {/* Mobile Header - positioned exactly like the main hero header */}\r\n                    <div className=\"h-[90px] relative\">\r\n                        <div className=\"absolute -bottom-10 left-0 w-full h-[1px] bg-black/10 z-[160]\"></div>\r\n                        <div className=\"container mx-auto max-w-[1536px] relative\">\r\n                            <div className=\"flex justify-between items-center relative z-[200] pt-6 px-8\">\r\n                                <Link href={\"/\"} className=\"relative z-[200] flex items-center gap-3\" onClick={(e) => e.stopPropagation()}>\r\n                                    <div className=\"bg-transparent p-1 rounded-full\">\r\n                                        <Image\r\n                                            className=\"w-10 h-auto md:w-30\"\r\n                                            width={70}\r\n                                            height={70}\r\n                                            priority\r\n                                            alt=\"Dfugo logo\"\r\n                                            src=\"/logo.png\"\r\n                                        />\r\n                                    </div>\r\n                                    <div>\r\n                                        <motion.span\r\n                                            className=\"text-base font-bold bg-clip-text text-transparent inline-block relative\"\r\n                                            style={{\r\n                                                backgroundImage: \"linear-gradient(90deg, #1E90FF, #FF69B4)\",\r\n                                                backgroundSize: \"200% 100%\",\r\n                                            }}\r\n                                            animate={{\r\n                                                backgroundPosition: [\"0% 0%\", \"100% 0%\", \"0% 0%\"],\r\n                                            }}\r\n                                            transition={{\r\n                                                duration: 5,\r\n                                                ease: \"linear\",\r\n                                                repeat: Infinity,\r\n                                            }}\r\n                                        >\r\n                                            CEPOKA BEAUTY HUB\r\n                                            <motion.div\r\n                                                className=\"absolute -bottom-1 left-0 h-[2px] rounded-full\"\r\n                                                style={{\r\n                                                    backgroundImage: \"linear-gradient(90deg, #1E90FF, #FF69B4)\",\r\n                                                    backgroundSize: \"200% 100%\",\r\n                                                }}\r\n                                                animate={{\r\n                                                    backgroundPosition: [\"0% 0%\", \"100% 0%\", \"0% 0%\"],\r\n                                                    width: [\"0%\", \"100%\"],\r\n                                                }}\r\n                                                transition={{\r\n                                                    backgroundPosition: {\r\n                                                        duration: 5,\r\n                                                        ease: \"linear\",\r\n                                                        repeat: Infinity,\r\n                                                    },\r\n                                                    width: {\r\n                                                        duration: 1,\r\n                                                        delay: 0.5,\r\n                                                        ease: \"easeOut\",\r\n                                                    },\r\n                                                }}\r\n                                            />\r\n                                        </motion.span>\r\n                                    </div>\r\n                                </Link>\r\n\r\n                                <motion.button\r\n                                    className=\"text-white focus:outline-none p-2 relative z-[170] cursor-pointer hover:text-gray-300 hover:scale-110 transition-all duration-200\"\r\n                                    onClick={(e) => {\r\n                                        e.stopPropagation();\r\n                                        handleMenuToggle(false);\r\n                                    }}\r\n                                    whileHover={{ scale: 1.1 }}\r\n                                    whileTap={{ scale: 0.9 }}\r\n                                    aria-label=\"Close menu\"\r\n                                >\r\n                                    <svg\r\n                                        className=\"w-6 h-6\"\r\n                                        fill=\"none\"\r\n                                        stroke=\"currentColor\"\r\n                                        viewBox=\"0 0 24 24\"\r\n                                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                                    >\r\n                                        <path\r\n                                            strokeLinecap=\"round\"\r\n                                            strokeLinejoin=\"round\"\r\n                                            strokeWidth=\"2\"\r\n                                            d=\"M6 18L18 6M6 6l12 12\"\r\n                                        />\r\n                                    </svg>\r\n                                </motion.button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div className=\"pt-16 pb-8 px-8 flex flex-col items-center gap-8\" onClick={(e) => e.stopPropagation()}>\r\n                        {/* Mobile Search - Centered */}\r\n                        <motion.form\r\n                            variants={menuItemVariants}\r\n                            custom={0}\r\n                            className=\"relative w-full max-w-[280px]\"\r\n                            onSubmit={handleSearch}\r\n                            onClick={(e) => e.stopPropagation()}\r\n                        >\r\n                            <input\r\n                                type=\"text\"\r\n                                value={searchQuery}\r\n                                onChange={(e) => setSearchQuery(e.target.value)}\r\n                                className=\"w-full px-6 py-3 rounded-full bg-white/10 text-white font-light focus:outline-none focus:ring-2 focus:ring-[#1E90FF] transition-all text-left placeholder-white/60\"\r\n                                placeholder=\"Search products...\"\r\n                            />\r\n                            <button\r\n                                type=\"submit\"\r\n                                className=\"absolute right-4 top-1/2 transform -translate-y-1/2 p-2\"\r\n                            >\r\n                                <Search className=\"w-4 h-4 text-white\" />\r\n                            </button>\r\n                        </motion.form>\r\n\r\n                        {/* Navigation Links - Centered with dots */}\r\n                        <div className=\"flex flex-col items-center gap-8 w-full\">\r\n                            {[\"Home\", \"Shop\", \"Contact\", \"About\"].map((link, i) => (\r\n                                <motion.div\r\n                                    key={link}\r\n                                    variants={menuItemVariants}\r\n                                    custom={i + 1}\r\n                                    className=\"relative flex flex-col items-center\"\r\n                                >\r\n                                    <motion.button\r\n                                        onClick={() => handleNavClick(link)}\r\n                                        className={`text-center text-lg font-medium py-2 px-4 ${activeLink === link ? \"text-[#FF69B4]\" : \"text-white\"}`}\r\n                                        whileHover={{ scale: 1.05 }}\r\n                                        whileTap={{ scale: 0.95 }}\r\n                                    >\r\n                                        {link}\r\n                                    </motion.button>\r\n                                    {activeLink === link && (\r\n                                        <div className=\"absolute -bottom-2 flex space-x-1\">\r\n                                            <motion.div\r\n                                                className=\"bg-[#1E90FF] w-[4px] h-[4px] rounded-full\"\r\n                                                initial={{ scale: 0 }}\r\n                                                animate={{ scale: 1 }}\r\n                                                transition={{ duration: 0.2 }}\r\n                                            />\r\n                                            <motion.div\r\n                                                className=\"bg-[#FF69B4] w-[4px] h-[4px] rounded-full\"\r\n                                                initial={{ scale: 0 }}\r\n                                                animate={{ scale: 1 }}\r\n                                                transition={{ duration: 0.2, delay: 0.1 }}\r\n                                            />\r\n                                            <motion.div\r\n                                                className=\"bg-[#1E90FF] w-[4px] h-[4px] rounded-full\"\r\n                                                initial={{ scale: 0 }}\r\n                                                animate={{ scale: 1 }}\r\n                                                transition={{ duration: 0.2, delay: 0.2 }}\r\n                                            />\r\n                                            <motion.div\r\n                                                className=\"bg-[#FF69B4] w-[4px] h-[4px] rounded-full\"\r\n                                                initial={{ scale: 0 }}\r\n                                                animate={{ scale: 1 }}\r\n                                                transition={{ duration: 0.2, delay: 0.3 }}\r\n                                            />\r\n                                        </div>\r\n                                    )}\r\n                                </motion.div>\r\n                            ))}\r\n                        </div>\r\n                    </div>\r\n                </motion.div>\r\n\r\n                {/* Desktop Navigation */}\r\n                <div className=\"hidden md:flex\">\r\n                    <div className=\"flex flex-col items-center\">\r\n                        {/* Nav items and search container */}\r\n                        <div className=\"flex items-center space-x-8 relative\">\r\n                            <motion.ul\r\n                                animate={{ opacity: showSearch ? 0 : 1 }}\r\n                                transition={{ duration: 0.2 }}\r\n                                className={`list-none flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-8 font-medium ${showSearch ? 'invisible' : 'visible'\r\n                                    }`}\r\n                            >\r\n                                {[\"Home\", \"Shop\", \"Contact\", \"About\"].map((link) => (\r\n                                    <li\r\n                                        key={link}\r\n                                        className={`relative cursor-pointer flex flex-col items-center ${activeLink === link ? \"text-[#1E90FF]\" : \"text-[#333333]\"\r\n                                            }`}\r\n                                        onClick={() => handleNavClick(link)}\r\n                                    >\r\n                                        <div>{link}</div>\r\n                                        {activeLink === link && (\r\n                                            <div className=\"absolute top-8 flex space-x-1\">\r\n                                                <motion.div\r\n                                                    className=\"bg-[#1E90FF] w-[4px] h-[4px] rounded-full\"\r\n                                                    initial={{ scale: 0 }}\r\n                                                    animate={{ scale: 1 }}\r\n                                                    transition={{ duration: 0.2 }}\r\n                                                />\r\n                                                <motion.div\r\n                                                    className=\"bg-[#FF69B4] w-[4px] h-[4px] rounded-full\"\r\n                                                    initial={{ scale: 0 }}\r\n                                                    animate={{ scale: 1 }}\r\n                                                    transition={{ duration: 0.2, delay: 0.1 }}\r\n                                                />\r\n                                                <motion.div\r\n                                                    className=\"bg-[#1E90FF] w-[4px] h-[4px] rounded-full\"\r\n                                                    initial={{ scale: 0 }}\r\n                                                    animate={{ scale: 1 }}\r\n                                                    transition={{ duration: 0.2, delay: 0.2 }}\r\n                                                />\r\n                                                <motion.div\r\n                                                    className=\"bg-[#FF69B4] w-[4px] h-[4px] rounded-full\"\r\n                                                    initial={{ scale: 0 }}\r\n                                                    animate={{ scale: 1 }}\r\n                                                    transition={{ duration: 0.2, delay: 0.3 }}\r\n                                                />\r\n                                            </div>\r\n                                        )}\r\n                                    </li>\r\n                                ))}\r\n                            </motion.ul>\r\n\r\n                            {/* Search container */}\r\n                            <div className=\"relative flex items-center\">\r\n                                <div\r\n                                    className=\"rounded-full p-3 cursor-pointer hover:bg-black/5 hidden md:block\"\r\n                                    onClick={() => setShowSearch(!showSearch)}\r\n                                >\r\n                                    <Search className=\"w-4 h-4 text-[#333333]\" />\r\n                                </div>\r\n\r\n                                {/* Inline search bar */}\r\n                                {showSearch && (\r\n                                    <motion.form\r\n                                        initial={{ opacity: 0, width: 0 }}\r\n                                        animate={{ opacity: 1, width: \"240px\" }}\r\n                                        exit={{ opacity: 0, width: 0 }}\r\n                                        onSubmit={handleSearch}\r\n                                        className=\"absolute right-12\"\r\n                                    >\r\n                                        <div className=\"relative\">\r\n                                            <input\r\n                                                type=\"text\"\r\n                                                value={searchQuery}\r\n                                                onChange={(e) => setSearchQuery(e.target.value)}\r\n                                                className=\"w-full px-4 py-2 rounded-full bg-black/5 text-[#333333] font-light focus:outline-none text-left placeholder-[#666666]\"\r\n                                                placeholder=\"Search products...\"\r\n                                                autoFocus\r\n                                                onBlur={() => !searchQuery && setShowSearch(false)}\r\n                                            />\r\n                                        </div>\r\n                                    </motion.form>\r\n                                )}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Main Content Section - Updated colors */}\r\n            <div className=\"cont2 w-full flex flex-col-reverse md:flex-row justify-around items-center px-8 md:px-20 lg:px-40 pb-10 mb-11 pt-28 mb:pt-0\">\r\n                {/* Hero Text Section - Updated colors */}\r\n                <div className=\"hero-text text-left pt-40 md:pt-0\">\r\n                    <motion.div\r\n                        initial={{ opacity: 0, y: -20 }}\r\n                        animate={{ opacity: 1, y: 0 }}\r\n                        transition={{ duration: 0.8, ease: \"easeOut\" }}\r\n                        className=\"py-3 mb-2\"\r\n                    >\r\n                        <motion.span\r\n                            className=\"text-ls md:text-2xl font-bold bg-clip-text text-transparent inline-block relative\"\r\n                            style={{\r\n                                backgroundImage: \"linear-gradient(90deg, #1E90FF, #FF69B4)\",\r\n                                backgroundSize: \"200% 100%\",\r\n                            }}\r\n                            animate={{\r\n                                backgroundPosition: [\"0% 0%\", \"100% 0%\", \"0% 0%\"],\r\n                            }}\r\n                            transition={{\r\n                                duration: 5,\r\n                                ease: \"linear\",\r\n                                repeat: Infinity,\r\n                            }}\r\n                        >\r\n                            CEPOKA BEAUTY HUB\r\n                            <motion.div\r\n                                className=\"absolute -top-1 left-0 h-[2px] rounded-full\"\r\n                                style={{\r\n                                    backgroundImage: \"linear-gradient(90deg, #1E90FF, #FF69B4)\",\r\n                                    backgroundSize: \"200% 100%\",\r\n                                }}\r\n                                animate={{\r\n                                    backgroundPosition: [\"0% 0%\", \"100% 0%\", \"0% 0%\"],\r\n                                    width: [\"0%\", \"100%\"],\r\n                                }}\r\n                                transition={{\r\n                                    backgroundPosition: {\r\n                                        duration: 5,\r\n                                        ease: \"linear\",\r\n                                        repeat: Infinity,\r\n                                    },\r\n                                    width: {\r\n                                        duration: 1,\r\n                                        delay: 0.5,\r\n                                        ease: \"easeOut\",\r\n                                    },\r\n                                }}\r\n                            />\r\n                        </motion.span>\r\n                    </motion.div>\r\n                    <div className=\"w-[60%] h-[1.5px] bg-gradient-to-r from-[#9a9a9a] to-transparent rounded-full mx-0\"></div>\r\n\r\n                    <motion.div\r\n                        variants={heroTextVariants}\r\n                        initial=\"hidden\"\r\n                        animate=\"visible\"\r\n                        className=\"text-3xl md:text-5xl py-5 font-[900] text-[#333333]\">\r\n                        {/* imlement modern text by text animation using framer */}\r\n                        <p>Where innovation</p>\r\n                        <div className=\"flex gap-2\">\r\n                            <span className=\"bg-gradient-to-r from-[#1E90FF] to-[#FF69B4] bg-clip-text text-transparent\">meets Beauty.</span>\r\n                        </div>\r\n                    </motion.div>\r\n                    <div className=\"w-[60%] h-[1.5px] bg-gradient-to-r from-[#9a9a9a] to-transparent rounded-full mx-0\"></div>\r\n                    <p className=\"pt-8 text-[14px] md:text-[20px] w-[80%] mx-0 font-normal text-[#333333]\">\r\n                        we offer all types of high quality salon, spa, and beauty equipments.\r\n                    </p>\r\n                    <div ref={actionsRef} className=\"quickact flex space-x-5 md:flex-row space-y-3 md:space-y-0 md:space-x-3 py-10 items-start md:items-center\">\r\n                        <Link href={\"/shop\"} onClick={() => setActiveLink(\"Shop\")}>\r\n                            <motion.button\r\n                                whileHover={{ scale: 1.1 }}\r\n                                whileTap={{ scale: 0.9 }}\r\n                                className=\"font-medium bg-gradient-to-tr from-[#1E90FF] to-[#FF69B4] text-white text-[14px] md:text-[20px] rounded-full p-2 px-8 gap-3 flex items-center\">\r\n                                Shop\r\n                                <ArrowRight size={20} />\r\n                            </motion.button>\r\n                        </Link>\r\n                        <motion.div\r\n                            whileHover={{ scale: 1.1 }}\r\n                            whileTap={{ scale: 0.9 }}\r\n                            className=\"flex flex-col relative cursor-pointer\"\r\n                            onClick={() => scrollToSection('section3')}>\r\n                            <div className=\"font-medium text-sm md:text-base text-[#333333]\">Locate Us</div>\r\n                            <div className=\"bg-gradient-to-r from-transparent via-[#c8c8c8] to-transparent h-[2px] w-full rounded-full\"></div>\r\n                        </motion.div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Highlight Section - Original position */}\r\n                <motion.div\r\n                    variants={highlightVariants}\r\n                    initial=\"hidden\"\r\n                    animate=\"visible\"\r\n                    className=\"highlight absolute top-[200px] md:relative md:top-0 w-48 md:w-80 flex items-center justify-center mt-3 md:mt-0\">\r\n                    {/* Model */}\r\n                    <motion.div variants={modelImageVariants} className=\"absolute w-52 md:w-auto top-[-130px] right-[20px] md:top-[-190px] md:right-[30px]\">\r\n                        <AnimatePresence mode=\"popLayout\">\r\n                            <motion.div\r\n                                key={currentModelIndex}\r\n                                variants={slideUpVariants}\r\n                                initial=\"enter\"\r\n                                animate=\"center\"\r\n                                exit=\"exit\"\r\n                                className=\"relative z-10\"\r\n                            >\r\n                                <Image\r\n                                    width={280}\r\n                                    height={160}\r\n                                    alt={`Model ${currentModelIndex + 1}`}\r\n                                    src={modelImages[currentModelIndex]}\r\n                                    className=\"w-52 md:w-80 z-10 relative\"\r\n                                    priority\r\n                                />\r\n                            </motion.div>\r\n                        </AnimatePresence>\r\n                        <div className=\"fade-boundary\"></div>\r\n                    </motion.div>\r\n\r\n                    {/* Highlight container */}\r\n                    <motion.div\r\n                        variants={cardVariants}\r\n                        initial=\"hidden\"\r\n                        animate=\"visible\"\r\n                        whileHover=\"hover\"\r\n                        className=\"w-[170px] md:w-[200px] highlight-box border-[1.5px] border-[#b2b2b2] rounded-[20px] p-[14px] md:p-[20px] md:rounded-[30px] z-10 space-y-2 md:space-y-4 absolute flex flex-col top-[-120px] md:top-[-15px]\"\r\n                        style={{\r\n                            transformStyle: \"preserve-3d\",\r\n                            perspective: \"1000px\"\r\n                        }}\r\n                    >\r\n                        <div className=\"flex justify-between items-center relative\">\r\n                            <div className=\"bg-[#d1d1d171] text-[8px] md:text-[12px] text-[#333333] font-semibold rounded-full p-1 px-3 md:px-5\">Featured</div>\r\n                            <motion.div\r\n                                whileHover={{ scale: 1.1 }}\r\n                                whileTap={{ scale: 0.9 }}\r\n                                className=\"p-1.5 md:p-4 rounded-full relative flex items-center justify-center cursor-pointer hover:bg-[#cccccc28]\"\r\n                            >\r\n                                <Image className=\"absolute w-3 md:w-4\" width={16} height={16} src={\"/icons/arrowwhite.png\"} alt=\"\" />\r\n                            </motion.div>\r\n                        </div>\r\n                        <motion.div\r\n                            className=\"flex justify-between space-y-1.5 md:space-y-3\"\r\n                            variants={{\r\n                                hidden: { y: 20, opacity: 0 },\r\n                                visible: {\r\n                                    y: 0,\r\n                                    opacity: 1,\r\n                                    transition: {\r\n                                        delay: 0.2,\r\n                                        duration: 0.5\r\n                                    }\r\n                                }\r\n                            }}\r\n                        >\r\n                            <h1 className=\"text-[14px] w-[5%] md:text[19px] text-[#333333] font-bold\">LED Facial Mask</h1>\r\n                            <div className=\"flex w-[60%] h-[60px] border-1 border-gray-500 bg-[#cccccc39] p-[6px] md:p-[10px] rounded-[8px]\">\r\n                                {/* <Image width={24} height={16} src={\"/images/wig1.png\"} alt=\"\" className=\"w-4 md:w-6\" /> */}\r\n                            </div>\r\n                        </motion.div>\r\n                    </motion.div>\r\n                </motion.div>\r\n            </div>\r\n\r\n            {/* Updated Scroll Button with visibility control */}\r\n            <div className={`absolute z-30 cursor-pointer bottom-10 transition-opacity duration-300 ${isOverlapping ? 'opacity-0 pointer-events-none' : 'opacity-100'}`}>\r\n                <motion.div\r\n                    ref={dropIconRef}\r\n                    whileHover={{ scale: 1.1 }}\r\n                    whileTap={{ scale: 0.9 }}\r\n                    className=\"relative w-10 h-10 md:w-14 md:h-14 flex items-center justify-center\"\r\n                    onClick={() => scrollToSection('section1')}\r\n                >\r\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-[#1E90FF] to-[#FF69B4] rounded-full\"></div>\r\n                    <div className=\"absolute inset-0.5 bg-white rounded-full flex items-center justify-center\">\r\n                        <ChevronDown className=\"w-6 h-6 text-[#1E90FF]\" />\r\n                    </div>\r\n                </motion.div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Hero;\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AAJA;AAKA;AAAA;AALA;AAKA;;;AARA;;;;;;;;AAUA,MAAM,OAAO;;IACT,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,gBAAa,AAAD;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,iCAAiC;IACjC,MAAM,mBAAmB,CAAC;QACtB,YAAY;IAChB;IACA,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,wBAAwB;IACxB,MAAM,cAAc;QAChB;QACA;QACA;KAEH;IAED,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACN,IAAI,CAAC,WAAW;YAEhB,MAAM,WAAW;2CAAY;oBACzB;mDAAqB,CAAC,YAClB,cAAc,YAAY,MAAM,GAAG,IAAI,IAAI,YAAY;;gBAE/D;0CAAG,OAAO,8CAA8C;YAExD;kCAAO,IAAM,cAAc;;QAC/B;yBAAG;QAAC;QAAW,YAAY,MAAM;KAAC;IAElC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACN,aAAa;QACjB;yBAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACrB,IAAI,CAAC,aAAa,aAAkB,aAAa;QACjD,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACT,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAChD;IACJ;IAEA,MAAM,iBAAiB,CAAC;QACpB,cAAc;QACd,YAAY;QAEZ,IAAI,CAAC,aAAa,aAAkB,aAAa;QAEjD,OAAQ,KAAK,WAAW;YACpB,KAAK;gBACD,gBAAgB;gBAChB;YACJ,KAAK;gBACD,gBAAgB;gBAChB;YACJ,KAAK;gBACD,QAAQ,SAAS;oBAAE,KAAK;oBAAG,UAAU;gBAAS;gBAC9C;YACJ,KAAK;gBACD,OAAO,IAAI,CAAC;gBACZ;QACR;IACJ;IAEA,MAAM,eAAe,CAAC;QAClB,GAAG;QACH,IAAI,YAAY,IAAI,IAAI;YACpB,OAAO,IAAI,CAAC,CAAC,aAAa,EAAE,mBAAmB,YAAY,IAAI,KAAK;QACxE;QACA,cAAc;QAEd,eAAe;IACnB;IAEA,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CAAE;YAC7B,IAAI,CAAC,aAAa,aAAkB,aAAa;YAEjD,MAAM,WAAW,YAAY,OAAO;YACpC,MAAM,UAAU,WAAW,OAAO;YAElC,IAAI,YAAY,WAAW,OAAO,UAAU,GAAG,KAAK;gBAChD,MAAM,WAAW,SAAS,qBAAqB;gBAC/C,MAAM,cAAc,QAAQ,qBAAqB;gBAEjD,2DAA2D;gBAC3D,MAAM,gBAAgB,CAAC,CACnB,SAAS,GAAG,GAAG,YAAY,MAAM,IACjC,SAAS,MAAM,GAAG,YAAY,GAAG,AACrC;gBAEA,iBAAiB;YACrB;QACJ;yCAAG;QAAC;KAAU;IAEd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACN,IAAI,CAAC,aAAa,aAAkB,aAAa;YAEjD;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC,OAAO,gBAAgB,CAAC,UAAU;YAElC;kCAAO;oBACH,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,UAAU;gBACzC;;QACJ;yBAAG;QAAC;QAAc;KAAU;IAE5B,kCAAkC;IAClC,MAAM,mBAAmB;QACrB,QAAQ;YAAE,SAAS;YAAG,GAAG,CAAC;QAAI;QAC9B,SAAS;YACL,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;gBAAG,MAAM;YAAU;QAC/C;IACJ;IAEA,MAAM,oBAAoB;QACtB,QAAQ;YAAE,SAAS;YAAG,OAAO;QAAI;QACjC,SAAS;YACL,SAAS;YACT,OAAO;YACP,YAAY;gBACR,UAAU;gBACV,MAAM;gBACN,OAAO;YACX;QACJ;IACJ;IAEA,MAAM,qBAAqB;QACvB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACL,GAAG;YACH,SAAS;YACT,YAAY;gBAAE,UAAU;gBAAG,MAAM;YAAU;QAC/C;IACJ;IAEA,MAAM,eAAe;QACjB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACL,GAAG;YACH,SAAS;YACT,YAAY;gBAAE,UAAU;gBAAG,MAAM;gBAAW,OAAO;YAAI;QAC3D;IACJ;IAEA,8CAA8C;IAE9C,MAAM,mBAAmB;QACrB,QAAQ;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC7B,MAAM,CAAC,IAAc,CAAC;gBAClB,SAAS;gBACT,GAAG;gBACH,YAAY;oBACR,OAAO,IAAI;oBACX,UAAU;oBACV,MAAM;wBAAC;wBAAK;wBAAG;wBAAK;qBAAE;gBAC1B;YACJ,CAAC;IACL;IAEA,yCAAyC;IACzC,MAAM,kBAAkB;QACpB,OAAO;YACH,UAAU;YACV,SAAS;QACb;QACA,QAAQ;YACJ,GAAG;YACH,SAAS;YACT,YAAY;gBACR,GAAG;oBAAE,UAAU;oBAAK,MAAM;wBAAC;wBAAM;wBAAG;wBAAK;qBAAE;gBAAC;gBAC5C,SAAS;oBAAE,UAAU;oBAAK,MAAM;wBAAC;wBAAM;wBAAG;wBAAK;qBAAE;gBAAC;YACtD;QACJ;QACA,MAAM;YACF,WAAW;YACX,SAAS;YACT,YAAY;gBACR,GAAG;oBAAE,UAAU;oBAAK,MAAM;wBAAC;wBAAM;wBAAG;wBAAK;qBAAE;gBAAC;gBAC5C,SAAS;oBAAE,UAAU;oBAAK,MAAM;wBAAC;wBAAM;wBAAG;wBAAK;qBAAE;gBAAC;YACtD;QACJ;IACJ;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBAAI,WAAU;;;;;;YAEd,0BACG,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACP,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,MAAM;oBAAE,SAAS;gBAAE;gBACnB,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;gBACV,SAAS,IAAM,iBAAiB;;;;;;0BAIxC,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAW,CAAC,uFAAuF,EAAE,WAAW,cAAc,cAAc,gCAAgC,CAAC;;0CAC9K,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM;gCAAK,WAAU;0CACvB,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACF,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,QAAQ;oCACR,KAAI;oCACJ,KAAI;;;;;;;;;;;4BAKX,CAAC,0BACE,6LAAC;gCACG,IAAG;gCACH,WAAU;gCACV,SAAS,CAAC;oCACN,EAAE,eAAe;oCACjB,iBAAiB;gCACrB;0CAEA,cAAA,6LAAC;oCACG,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,SAAQ;oCACR,OAAM;8CAEN,cAAA,6LAAC;wCACG,eAAc;wCACd,gBAAe;wCACf,aAAY;wCACZ,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAQtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACP,IAAG;wBACH,SAAQ;wBACR,SAAS,WAAW,SAAS;wBAC7B,UAAU;4BACN,MAAM;gCACF,SAAS;gCACT,GAAG;gCACH,YAAY;oCACR,UAAU;oCACV,MAAM;wCAAC;wCAAK;wCAAG;wCAAK;qCAAE;oCACtB,iBAAiB;gCACrB;4BACJ;4BACA,QAAQ;gCACJ,SAAS;gCACT,GAAG;gCACH,YAAY;oCACR,UAAU;oCACV,MAAM;wCAAC;wCAAK;wCAAG;wCAAK;qCAAE;oCACtB,iBAAiB;oCACjB,kBAAkB,CAAC;gCACvB;4BACJ;wBACJ;wBACA,WAAU;wBACV,SAAS,CAAC;4BACN,EAAE,eAAe;4BACjB,iBAAiB;wBACrB;;0CAGA,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC;4CAAI,WAAU;;8DACX,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAM;oDAAK,WAAU;oDAA2C,SAAS,CAAC,IAAM,EAAE,eAAe;;sEACnG,6LAAC;4DAAI,WAAU;sEACX,cAAA,6LAAC,gIAAA,CAAA,UAAK;gEACF,WAAU;gEACV,OAAO;gEACP,QAAQ;gEACR,QAAQ;gEACR,KAAI;gEACJ,KAAI;;;;;;;;;;;sEAGZ,6LAAC;sEACG,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gEACR,WAAU;gEACV,OAAO;oEACH,iBAAiB;oEACjB,gBAAgB;gEACpB;gEACA,SAAS;oEACL,oBAAoB;wEAAC;wEAAS;wEAAW;qEAAQ;gEACrD;gEACA,YAAY;oEACR,UAAU;oEACV,MAAM;oEACN,QAAQ;gEACZ;;oEACH;kFAEG,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wEACP,WAAU;wEACV,OAAO;4EACH,iBAAiB;4EACjB,gBAAgB;wEACpB;wEACA,SAAS;4EACL,oBAAoB;gFAAC;gFAAS;gFAAW;6EAAQ;4EACjD,OAAO;gFAAC;gFAAM;6EAAO;wEACzB;wEACA,YAAY;4EACR,oBAAoB;gFAChB,UAAU;gFACV,MAAM;gFACN,QAAQ;4EACZ;4EACA,OAAO;gFACH,UAAU;gFACV,OAAO;gFACP,MAAM;4EACV;wEACJ;;;;;;;;;;;;;;;;;;;;;;;8DAMhB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oDACV,WAAU;oDACV,SAAS,CAAC;wDACN,EAAE,eAAe;wDACjB,iBAAiB;oDACrB;oDACA,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAI;oDACvB,cAAW;8DAEX,cAAA,6LAAC;wDACG,WAAU;wDACV,MAAK;wDACL,QAAO;wDACP,SAAQ;wDACR,OAAM;kEAEN,cAAA,6LAAC;4DACG,eAAc;4DACd,gBAAe;4DACf,aAAY;4DACZ,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ1B,6LAAC;gCAAI,WAAU;gCAAmD,SAAS,CAAC,IAAM,EAAE,eAAe;;kDAE/F,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;wCACR,UAAU;wCACV,QAAQ;wCACR,WAAU;wCACV,UAAU;wCACV,SAAS,CAAC,IAAM,EAAE,eAAe;;0DAEjC,6LAAC;gDACG,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;gDACV,aAAY;;;;;;0DAEhB,6LAAC;gDACG,MAAK;gDACL,WAAU;0DAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAK1B,6LAAC;wCAAI,WAAU;kDACV;4CAAC;4CAAQ;4CAAQ;4CAAW;yCAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,kBAC7C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAEP,UAAU;gDACV,QAAQ,IAAI;gDACZ,WAAU;;kEAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wDACV,SAAS,IAAM,eAAe;wDAC9B,WAAW,CAAC,0CAA0C,EAAE,eAAe,OAAO,mBAAmB,cAAc;wDAC/G,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;kEAEvB;;;;;;oDAEJ,eAAe,sBACZ,6LAAC;wDAAI,WAAU;;0EACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEACP,WAAU;gEACV,SAAS;oEAAE,OAAO;gEAAE;gEACpB,SAAS;oEAAE,OAAO;gEAAE;gEACpB,YAAY;oEAAE,UAAU;gEAAI;;;;;;0EAEhC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEACP,WAAU;gEACV,SAAS;oEAAE,OAAO;gEAAE;gEACpB,SAAS;oEAAE,OAAO;gEAAE;gEACpB,YAAY;oEAAE,UAAU;oEAAK,OAAO;gEAAI;;;;;;0EAE5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEACP,WAAU;gEACV,SAAS;oEAAE,OAAO;gEAAE;gEACpB,SAAS;oEAAE,OAAO;gEAAE;gEACpB,YAAY;oEAAE,UAAU;oEAAK,OAAO;gEAAI;;;;;;0EAE5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEACP,WAAU;gEACV,SAAS;oEAAE,OAAO;gEAAE;gEACpB,SAAS;oEAAE,OAAO;gEAAE;gEACpB,YAAY;oEAAE,UAAU;oEAAK,OAAO;gEAAI;;;;;;;;;;;;;+CArC/C;;;;;;;;;;;;;;;;;;;;;;kCAgDzB,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;sCAEX,cAAA,6LAAC;gCAAI,WAAU;;kDACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wCACN,SAAS;4CAAE,SAAS,aAAa,IAAI;wCAAE;wCACvC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAW,CAAC,iGAAiG,EAAE,aAAa,cAAc,WACpI;kDAEL;4CAAC;4CAAQ;4CAAQ;4CAAW;yCAAQ,CAAC,GAAG,CAAC,CAAC,qBACvC,6LAAC;gDAEG,WAAW,CAAC,mDAAmD,EAAE,eAAe,OAAO,mBAAmB,kBACpG;gDACN,SAAS,IAAM,eAAe;;kEAE9B,6LAAC;kEAAK;;;;;;oDACL,eAAe,sBACZ,6LAAC;wDAAI,WAAU;;0EACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEACP,WAAU;gEACV,SAAS;oEAAE,OAAO;gEAAE;gEACpB,SAAS;oEAAE,OAAO;gEAAE;gEACpB,YAAY;oEAAE,UAAU;gEAAI;;;;;;0EAEhC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEACP,WAAU;gEACV,SAAS;oEAAE,OAAO;gEAAE;gEACpB,SAAS;oEAAE,OAAO;gEAAE;gEACpB,YAAY;oEAAE,UAAU;oEAAK,OAAO;gEAAI;;;;;;0EAE5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEACP,WAAU;gEACV,SAAS;oEAAE,OAAO;gEAAE;gEACpB,SAAS;oEAAE,OAAO;gEAAE;gEACpB,YAAY;oEAAE,UAAU;oEAAK,OAAO;gEAAI;;;;;;0EAE5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEACP,WAAU;gEACV,SAAS;oEAAE,OAAO;gEAAE;gEACpB,SAAS;oEAAE,OAAO;gEAAE;gEACpB,YAAY;oEAAE,UAAU;oEAAK,OAAO;gEAAI;;;;;;;;;;;;;+CA9B/C;;;;;;;;;;kDAuCjB,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDACG,WAAU;gDACV,SAAS,IAAM,cAAc,CAAC;0DAE9B,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;4CAIrB,4BACG,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gDACR,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDAChC,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAQ;gDACtC,MAAM;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDAC7B,UAAU;gDACV,WAAU;0DAEV,cAAA,6LAAC;oDAAI,WAAU;8DACX,cAAA,6LAAC;wDACG,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wDAC9C,WAAU;wDACV,aAAY;wDACZ,SAAS;wDACT,QAAQ,IAAM,CAAC,eAAe,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYhF,6LAAC;gBAAI,WAAU;;kCAEX,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,MAAM;gCAAU;gCAC7C,WAAU;0CAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oCACR,WAAU;oCACV,OAAO;wCACH,iBAAiB;wCACjB,gBAAgB;oCACpB;oCACA,SAAS;wCACL,oBAAoB;4CAAC;4CAAS;4CAAW;yCAAQ;oCACrD;oCACA,YAAY;wCACR,UAAU;wCACV,MAAM;wCACN,QAAQ;oCACZ;;wCACH;sDAEG,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACP,WAAU;4CACV,OAAO;gDACH,iBAAiB;gDACjB,gBAAgB;4CACpB;4CACA,SAAS;gDACL,oBAAoB;oDAAC;oDAAS;oDAAW;iDAAQ;gDACjD,OAAO;oDAAC;oDAAM;iDAAO;4CACzB;4CACA,YAAY;gDACR,oBAAoB;oDAChB,UAAU;oDACV,MAAM;oDACN,QAAQ;gDACZ;gDACA,OAAO;oDACH,UAAU;oDACV,OAAO;oDACP,MAAM;gDACV;4CACJ;;;;;;;;;;;;;;;;;0CAIZ,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACP,UAAU;gCACV,SAAQ;gCACR,SAAQ;gCACR,WAAU;;kDAEV,6LAAC;kDAAE;;;;;;kDACH,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC;4CAAK,WAAU;sDAA6E;;;;;;;;;;;;;;;;;0CAGrG,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAA0E;;;;;;0CAGvF,6LAAC;gCAAI,KAAK;gCAAY,WAAU;;kDAC5B,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM;wCAAS,SAAS,IAAM,cAAc;kDAC9C,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CACV,YAAY;gDAAE,OAAO;4CAAI;4CACzB,UAAU;gDAAE,OAAO;4CAAI;4CACvB,WAAU;;gDAAgJ;8DAE1J,6LAAC,qNAAA,CAAA,aAAU;oDAAC,MAAM;;;;;;;;;;;;;;;;;kDAG1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACP,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAI;wCACvB,WAAU;wCACV,SAAS,IAAM,gBAAgB;;0DAC/B,6LAAC;gDAAI,WAAU;0DAAkD;;;;;;0DACjE,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAM3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACP,UAAU;wBACV,SAAQ;wBACR,SAAQ;wBACR,WAAU;;0CAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;gCAAoB,WAAU;;kDAChD,6LAAC,4LAAA,CAAA,kBAAe;wCAAC,MAAK;kDAClB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAEP,UAAU;4CACV,SAAQ;4CACR,SAAQ;4CACR,MAAK;4CACL,WAAU;sDAEV,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDACF,OAAO;gDACP,QAAQ;gDACR,KAAK,CAAC,MAAM,EAAE,oBAAoB,GAAG;gDACrC,KAAK,WAAW,CAAC,kBAAkB;gDACnC,WAAU;gDACV,QAAQ;;;;;;2CAbP;;;;;;;;;;kDAiBb,6LAAC;wCAAI,WAAU;;;;;;;;;;;;0CAInB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACP,UAAU;gCACV,SAAQ;gCACR,SAAQ;gCACR,YAAW;gCACX,WAAU;gCACV,OAAO;oCACH,gBAAgB;oCAChB,aAAa;gCACjB;;kDAEA,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;0DAAsG;;;;;;0DACrH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACP,YAAY;oDAAE,OAAO;gDAAI;gDACzB,UAAU;oDAAE,OAAO;gDAAI;gDACvB,WAAU;0DAEV,cAAA,6LAAC,gIAAA,CAAA,UAAK;oDAAC,WAAU;oDAAsB,OAAO;oDAAI,QAAQ;oDAAI,KAAK;oDAAyB,KAAI;;;;;;;;;;;;;;;;;kDAGxG,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACP,WAAU;wCACV,UAAU;4CACN,QAAQ;gDAAE,GAAG;gDAAI,SAAS;4CAAE;4CAC5B,SAAS;gDACL,GAAG;gDACH,SAAS;gDACT,YAAY;oDACR,OAAO;oDACP,UAAU;gDACd;4CACJ;wCACJ;;0DAEA,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAC1E,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/B,6LAAC;gBAAI,WAAW,CAAC,uEAAuE,EAAE,gBAAgB,kCAAkC,eAAe;0BACvJ,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACP,KAAK;oBACL,YAAY;wBAAE,OAAO;oBAAI;oBACzB,UAAU;wBAAE,OAAO;oBAAI;oBACvB,WAAU;oBACV,SAAS,IAAM,gBAAgB;;sCAE/B,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM/C;GA5sBM;;QACoC,8IAAA,CAAA,gBAAa;QAOpC,qIAAA,CAAA,YAAS;;;KARtB;uCA8sBS"}}, {"offset": {"line": 1405, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1411, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/Offer.tsx"], "sourcesContent": ["\"use client\";\r\nimport Image from \"next/image\";\r\nimport { useInView } from \"react-intersection-observer\";\r\nimport { motion, Variants } from \"framer-motion\";\r\n\r\ninterface CardProps {\r\n  imgSrc: string;\r\n  text: string;\r\n  cardVariants: Variants;\r\n  delay?: number;\r\n}\r\n\r\nconst Offer: React.FC = () => {\r\n  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;\r\n  \r\n  const cardVariants: Variants = {\r\n    hidden: {\r\n      opacity: 0,\r\n      x: isMobile ? -50 : 0,  // Increased offset on mobile for more visible slide\r\n      y: !isMobile ? 100 : 0\r\n    },\r\n    visible: (delay: number = 0) => ({\r\n      opacity: 1,\r\n      x: 0,\r\n      y: 0,\r\n      transition: {\r\n        type: \"spring\", // Using spring for smoother animation\r\n        stiffness: 50, // Lower stiffness for smoother movement\r\n        damping: 15,   // Adjusted damping for better bounce\r\n        duration: isMobile ? 0.7 : 0.8, // Slightly longer duration on mobile\r\n        delay: isMobile ? delay * 0.2 : delay // Adjusted delay between items\r\n      }\r\n    })\r\n  };\r\n\r\n  return (\r\n    <div className=\"offer flex flex-col items-center w-full\">\r\n      <div className=\"w-full md:max-w-5xl flex flex-col md:flex-row justify-center items-center space-y-10 md:space-y-0 text-[#333] md:bg-[#ededed] md:px-16 md:py-12 md:rounded-[30px]\">\r\n        <Card\r\n          imgSrc=\"/icons/diamond.png\"\r\n          text=\"Fast and Safe delivery.\"\r\n          cardVariants={cardVariants}\r\n          delay={0}\r\n        />\r\n        <div className=\"hidden md:block w-[1px] h-32 bg-gradient-to-b from-transparent via-gray-400 to-transparent mx-16\" />\r\n        <Card\r\n          imgSrc=\"/icons/call.png\"\r\n          text=\"24/7 Customer Support.\"\r\n          cardVariants={cardVariants}\r\n          delay={0.2}\r\n        />\r\n        <div className=\"hidden md:block w-[1px] h-32 bg-gradient-to-b from-transparent via-gray-400 to-transparent mx-16\" />\r\n        <Card\r\n          imgSrc=\"/icons/package.png\"\r\n          text=\"Secure Packaging.\"\r\n          cardVariants={cardVariants}\r\n          delay={0.4}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst Card: React.FC<CardProps> = ({ imgSrc, text, cardVariants, delay = 0 }) => {\r\n  const [ref, inView] = useInView({ \r\n    triggerOnce: true, \r\n    threshold: 0.2,\r\n    rootMargin: \"10px 50px -10px 50px\"\r\n  });\r\n\r\n  return (\r\n    <motion.div\r\n      ref={ref}\r\n      className=\"card w-full px-4 md:px-0 md:w-52 h-40 md:h-52 p-3 bg-gradient-to-r from-[#ededed] to-transparent md:bg-transparent flex items-center justify-center\"\r\n      initial=\"hidden\"\r\n      animate={inView ? \"visible\" : \"hidden\"}\r\n      variants={cardVariants}\r\n      custom={delay}\r\n    >\r\n      <div className=\"flex flex-row md:flex-col items-center space-x-4 md:space-x-0 md:space-y-4\">\r\n        <div className=\"relative w-24 h-24 flex items-center justify-center overflow-visible\">\r\n          <Image\r\n            width={40}\r\n            height={40}\r\n            alt=\"\"\r\n            src={imgSrc}\r\n            className=\"transition-transform duration-300 ease-in-out\"\r\n          />\r\n        </div>\r\n        <p className=\"font-medium text-left md:text-center\">{text}</p>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n};\r\n\r\nexport default Offer;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAYA,MAAM,QAAkB;IACtB,MAAM,WAAW,aAAkB,eAAe,OAAO,UAAU,GAAG;IAEtE,MAAM,eAAyB;QAC7B,QAAQ;YACN,SAAS;YACT,GAAG,WAAW,CAAC,KAAK;YACpB,GAAG,CAAC,WAAW,MAAM;QACvB;QACA,SAAS,CAAC,QAAgB,CAAC,GAAK,CAAC;gBAC/B,SAAS;gBACT,GAAG;gBACH,GAAG;gBACH,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;oBACT,UAAU,WAAW,MAAM;oBAC3B,OAAO,WAAW,QAAQ,MAAM,MAAM,+BAA+B;gBACvE;YACF,CAAC;IACH;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBACC,QAAO;oBACP,MAAK;oBACL,cAAc;oBACd,OAAO;;;;;;8BAET,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBACC,QAAO;oBACP,MAAK;oBACL,cAAc;oBACd,OAAO;;;;;;8BAET,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBACC,QAAO;oBACP,MAAK;oBACL,cAAc;oBACd,OAAO;;;;;;;;;;;;;;;;;AAKjB;KAjDM;AAmDN,MAAM,OAA4B,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,CAAC,EAAE;;IAC1E,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;QACX,YAAY;IACd;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAU;QACV,SAAQ;QACR,SAAS,SAAS,YAAY;QAC9B,UAAU;QACV,QAAQ;kBAER,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wBACJ,OAAO;wBACP,QAAQ;wBACR,KAAI;wBACJ,KAAK;wBACL,WAAU;;;;;;;;;;;8BAGd,6LAAC;oBAAE,WAAU;8BAAwC;;;;;;;;;;;;;;;;;AAI7D;GA9BM;;QACkB,sKAAA,CAAA,YAAS;;;MAD3B;uCAgCS"}}, {"offset": {"line": 1575, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1581, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/SpinningLoader.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface SpinningLoaderProps {\n  size?: 'small' | 'medium' | 'large';\n  className?: string;\n  text?: string;\n}\n\nconst SpinningLoader: React.FC<SpinningLoaderProps> = ({ \n  size = 'medium', \n  className = '',\n  text\n}) => {\n  // Size mapping\n  const sizeMap = {\n    small: 'w-6 h-6 border-2',\n    medium: 'w-10 h-10 border-3',\n    large: 'w-16 h-16 border-4',\n  };\n\n  const sizeClass = sizeMap[size];\n  \n  return (\n    <div className={`flex flex-col items-center justify-center ${className}`}>\n      <motion.div\n        className={`${sizeClass} rounded-full border-t-pink-500 border-r-blue-500 border-b-pink-500 border-l-blue-500`}\n        animate={{ rotate: 360 }}\n        transition={{\n          duration: 1.5,\n          repeat: Infinity,\n          ease: \"linear\"\n        }}\n        style={{ borderStyle: 'solid' }}\n      />\n      {text && (\n        <p className=\"mt-3 text-sm text-gray-600 font-medium\">{text}</p>\n      )}\n    </div>\n  );\n};\n\nexport default SpinningLoader;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAWA,MAAM,iBAAgD,CAAC,EACrD,OAAO,QAAQ,EACf,YAAY,EAAE,EACd,IAAI,EACL;IACC,eAAe;IACf,MAAM,UAAU;QACd,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,YAAY,OAAO,CAAC,KAAK;IAE/B,qBACE,6LAAC;QAAI,WAAW,CAAC,0CAA0C,EAAE,WAAW;;0BACtE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,GAAG,UAAU,qFAAqF,CAAC;gBAC9G,SAAS;oBAAE,QAAQ;gBAAI;gBACvB,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;gBACA,OAAO;oBAAE,aAAa;gBAAQ;;;;;;YAE/B,sBACC,6LAAC;gBAAE,WAAU;0BAA0C;;;;;;;;;;;;AAI/D;KA/BM;uCAiCS"}}, {"offset": {"line": 1640, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1646, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/lib/appwrite.ts"], "sourcesContent": ["import { Client, Account, Databases, Storage } from \"appwrite\";\r\n\r\n// Initialize the Appwrite client\r\nconst client = new Client()\r\n  .setEndpoint(\"https://cloud.appwrite.io/v1\")\r\n  .setProject(\"67d07dc9000bafdd5d81\"); // Confirmed correct project ID\r\n\r\nexport const account = new Account(client);\r\nexport const databases = new Databases(client);\r\nexport const storage = new Storage(client);\r\n\r\nexport const appwriteConfig = {\r\n  // Using the confirmed database ID\r\n  databaseId: \"6813eadb003e7d64f63c\",\r\n  productsCollectionId: \"6813eaf40036e52c29b1\",\r\n  categoriesCollectionId: \"6817640f000dd0b67c77\",\r\n  stockProductsCollectionId: \"681a651d001cc3de8395\",\r\n  stockMovementsCollectionId: \"681bddcc000204a3748d\",\r\n  storageId: \"6813ea36001624c1202a\",\r\n};\r\n\r\n// project id: 67d07d7b0010f39ec77d\r\n// database id: 67d8833d000778157021\r\n// collection id: 67d8835b002502c5d7ba\r\n// storage id: 67d8841a001213adf116\r\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,iCAAiC;AACjC,MAAM,SAAS,IAAI,iJAAA,CAAA,SAAM,GACtB,WAAW,CAAC,gCACZ,UAAU,CAAC,yBAAyB,+BAA+B;AAE/D,MAAM,UAAU,IAAI,iJAAA,CAAA,UAAO,CAAC;AAC5B,MAAM,YAAY,IAAI,iJAAA,CAAA,YAAS,CAAC;AAChC,MAAM,UAAU,IAAI,iJAAA,CAAA,UAAO,CAAC;AAE5B,MAAM,iBAAiB;IAC5B,kCAAkC;IAClC,YAAY;IACZ,sBAAsB;IACtB,wBAAwB;IACxB,2BAA2B;IAC3B,4BAA4B;IAC5B,WAAW;AACb,GAEA,mCAAmC;CACnC,oCAAoC;CACpC,sCAAsC;CACtC,mCAAmC"}}, {"offset": {"line": 1674, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1680, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/LatestProduct.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport SpinningLoader from \"./SpinningLoader\";\r\nimport Image from \"next/image\";\r\nimport { motion, Variants } from \"framer-motion\";\r\nimport { useInView } from \"react-intersection-observer\";\r\nimport Link from \"next/link\";\r\nimport { useActiveLink } from \"../context/ActiveLinkContext\";\r\nimport { Heart, ArrowRight } from \"lucide-react\";\r\nimport { databases, appwriteConfig } from '@/src/lib/appwrite';\r\nimport { Query } from 'appwrite';\r\n\r\ninterface Product {\r\n  $id: string;\r\n  name: string;\r\n  price: string;\r\n  description: string;\r\n  imageUrls: string[];\r\n  $createdAt: string;\r\n}\r\n\r\nconst LatestProduct: React.FC = () => {\r\n  const { setActiveLink } = useActiveLink();\r\n  const [products, setProducts] = useState<Product[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [loadingProduct, setLoadingProduct] = useState(false);\r\n  const [likedProducts, setLikedProducts] = useState<{ [key: string]: boolean }>({});\r\n\r\n  const { ref: containerRef, inView: containerInView } = useInView({\r\n    triggerOnce: true,\r\n    threshold: 0.1,\r\n    rootMargin: \"0px 0px -100px 0px\" // Trigger animation before element is fully in view\r\n  });\r\n\r\n  useEffect(() => {\r\n\r\n    console.log(products.map((product) => product.imageUrls));\r\n\r\n  }, [products])\r\n\r\n  // Fetch latest products from Appwrite\r\n  useEffect(() => {\r\n    const fetchLatestProducts = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const response = await databases.listDocuments(\r\n          appwriteConfig.databaseId,\r\n          appwriteConfig.productsCollectionId,\r\n          [\r\n            Query.orderDesc('$createdAt'), // Order by creation date, newest first\r\n            Query.limit(4) // Get only 4 products\r\n          ]\r\n        );\r\n        setProducts(response.documents.map(doc => ({\r\n          $id: doc.$id,\r\n          name: doc.name,\r\n          price: doc.price,\r\n          description: doc.description,\r\n          imageUrls: doc.imageUrls,\r\n          $createdAt: doc.$createdAt\r\n        })));\r\n      } catch (error) {\r\n        console.error('Error fetching latest products:', error);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchLatestProducts();\r\n  }, []);\r\n\r\n  const toggleLike = (productId: string) => {\r\n    setLikedProducts(prev => ({\r\n      ...prev,\r\n      [productId]: !prev[productId]\r\n    }));\r\n  };\r\n\r\n  // Container animation variants\r\n  const containerVariants: Variants = {\r\n    hidden: {\r\n      opacity: 0,\r\n      y: 20,\r\n    },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: {\r\n        type: \"spring\",\r\n        stiffness: 50,\r\n        damping: 20,\r\n        staggerChildren: 0.1, // Stagger the animation of children\r\n        delayChildren: 0.2,   // Delay before starting children animations\r\n        when: \"beforeChildren\"\r\n      }\r\n    }\r\n  };\r\n\r\n  // Card animation variants\r\n  const cardVariants: Variants = {\r\n    hidden: {\r\n      opacity: 0,\r\n      y: 50,\r\n      scale: 0.9,\r\n      rotateX: 10,\r\n    },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      scale: 1,\r\n      rotateX: 0,\r\n      transition: {\r\n        type: \"spring\",\r\n        stiffness: 100,\r\n        damping: 15,\r\n        duration: 0.5,\r\n        delay: 0.1\r\n      }\r\n    },\r\n    hover: {\r\n      y: -12,\r\n      scale: 1.05,\r\n      transition: {\r\n        type: \"spring\",\r\n        stiffness: 400,\r\n        damping: 20\r\n      },\r\n      boxShadow: \"0px 20px 25px -5px rgba(0, 0, 0, 0.1), 0px 8px 10px -6px rgba(0, 0, 0, 0.1)\"\r\n    },\r\n    tap: {\r\n      scale: 0.98,\r\n      y: -5,\r\n      boxShadow: \"0px 10px 15px -3px rgba(0, 0, 0, 0.1), 0px 4px 6px -4px rgba(0, 0, 0, 0.1)\",\r\n      transition: {\r\n        type: \"spring\",\r\n        stiffness: 400,\r\n        damping: 15\r\n      }\r\n    }\r\n\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"latest-product-container flex flex-col items-center space-y-12\">\r\n        <SpinningLoader size=\"large\" text=\"Loading products...\" />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"latest-product-container flex flex-col items-center space-y-8 relative\">\r\n      {/* Loading overlay for product clicks */}\r\n      {loadingProduct && (\r\n        <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center\">\r\n          <div className=\"bg-white rounded-lg p-6 shadow-xl flex flex-col items-center\">\r\n            <SpinningLoader size=\"large\" text=\"Loading product...\" />\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {loading ? (\r\n        <motion.div\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          className=\"w-full flex justify-center items-center py-16\"\r\n        >\r\n          <div className=\"relative\">\r\n            <SpinningLoader size=\"large\" text=\"Loading latest products...\" />\r\n          </div>\r\n        </motion.div>\r\n      ) : (\r\n        <motion.div\r\n          ref={containerRef}\r\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-4 w-full px-4 md:px-8 mx-auto\"\r\n          variants={containerVariants}\r\n          initial=\"hidden\"\r\n          animate={containerInView ? \"visible\" : \"hidden\"}\r\n        >\r\n          {products.map((product) => (\r\n            <Link\r\n              href={`/product/${product.name.toLowerCase().replace(/\\s+/g, '-')}`}\r\n              key={product.$id}\r\n              onClick={() => {\r\n                // Show loading screen\r\n                setLoadingProduct(true);\r\n                // Prevent scrolling\r\n                document.body.style.overflow = 'hidden';\r\n\r\n                localStorage.setItem(\"selectedProduct\", JSON.stringify({\r\n                  name: product.name,\r\n                  price: product.price,\r\n                  description: product.description,\r\n                  imageUrls: product.imageUrls\r\n                }));\r\n              }}\r\n            >\r\n              <motion.div\r\n                className=\"latest-product-card w-[160px] sm:w-[220px] h-[220px] sm:h-[280px] flex flex-col items-center mx-auto\r\n                relative rounded-[15px] sm:rounded-[25px] cursor-pointer transition-colors duration-200 overflow-hidden\"\r\n                variants={cardVariants}\r\n                whileHover={{ y: -5 }}\r\n                whileTap={{ scale: 0.98 }}\r\n                style={{\r\n                  backfaceVisibility: \"hidden\",\r\n                  WebkitFontSmoothing: \"subpixel-antialiased\"\r\n                }}\r\n              >\r\n                {/* Product Image */}\r\n                <div className=\"w-full h-full relative\">\r\n                  {product.imageUrls && product.imageUrls.length > 0 ? (\r\n                    <Image\r\n                      className=\"object-cover\"\r\n                      fill\r\n                      alt={product.name || \"Product image\"}\r\n                      src={product.imageUrls[0]}\r\n                      unoptimized={true}\r\n                      sizes=\"(max-width: 640px) 160px, 220px\"\r\n                      style={{ objectFit: 'cover' }}\r\n                    />\r\n                  ) : (\r\n                    <div className=\"w-full h-full flex items-center justify-center bg-gray-200\">\r\n                      <p className=\"text-gray-400\">No Image</p>\r\n                    </div>\r\n                  )}\r\n                  {/* Gradient Overlay */}\r\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-black/10 to-transparent\" />\r\n                </div>\r\n\r\n                {/* Heart icon */}\r\n                <div\r\n                  onClick={(e) => {\r\n                    e.preventDefault();\r\n                    e.stopPropagation();\r\n                    toggleLike(product.$id);\r\n                  }}\r\n                  className=\"absolute right-2 sm:right-4 top-2 sm:top-4 z-10 p-1 sm:p-2 cursor-pointer hover:scale-110 transition-transform\"\r\n                >\r\n                  <Heart\r\n                    className=\"stroke-none\"\r\n                    fill={likedProducts[product.$id] ? \"#ff3b5c\" : \"#ffffff50\"}\r\n                    size={24}\r\n                    strokeWidth={1}\r\n                  />\r\n                </div>\r\n\r\n                {/* Price Card */}\r\n                <div className=\"price-card w-[90%] h-[70px] sm:h-[80px] rounded-[10px] sm:rounded-[15px]\r\n                  absolute bottom-3 bg-gradient-to-r from-black/80 to-black/40 backdrop-blur-[2px]\r\n                  flex flex-col justify-center gap-1 sm:gap-2\">\r\n                  <div className=\"px-3 sm:px-4 text-white font-semibold text-xs sm:text-sm truncate\">\r\n                    {product.name}\r\n                  </div>\r\n                  <div className=\"w-full h-[1px] bg-[#dddd]\"></div>\r\n                  <div className=\"flex items-center justify-between px-3 sm:px-4\">\r\n                    <p className=\"text-white font-medium text-xs sm:text-sm\">₦{product.price}</p>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            </Link>\r\n          ))}\r\n        </motion.div>\r\n      )}\r\n\r\n      {/* Shop button */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={containerInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\r\n        transition={{ delay: 0.5, duration: 0.5 }}\r\n        className=\"mt-4 md:mt-8\"\r\n      >\r\n        <Link href={\"/shop\"} onClick={() => setActiveLink(\"Shop\")}>\r\n          <motion.button\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            className=\"font-medium bg-gradient-to-tr from-[#1E90FF] to-[#FF69B4] text-white\r\n                text-base sm:text-[20px] rounded-full p-2 px-8 flex items-center gap-2 shadow-lg\"\r\n          >\r\n            Shop\r\n            <ArrowRight size={20} />\r\n          </motion.button>\r\n        </Link>\r\n      </motion.div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LatestProduct;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAGA;AACA;AAEA;AACA;AALA;AADA;AAIA;AAAA;;;AARA;;;;;;;;;;;AAqBA,MAAM,gBAA0B;;IAC9B,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,gBAAa,AAAD;IACtC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B,CAAC;IAEhF,MAAM,EAAE,KAAK,YAAY,EAAE,QAAQ,eAAe,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAC/D,aAAa;QACb,WAAW;QACX,YAAY,qBAAqB,oDAAoD;IACvF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YAER,QAAQ,GAAG,CAAC,SAAS,GAAG;2CAAC,CAAC,UAAY,QAAQ,SAAS;;QAEzD;kCAAG;QAAC;KAAS;IAEb,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;+DAAsB;oBAC1B,IAAI;wBACF,WAAW;wBACX,MAAM,WAAW,MAAM,yHAAA,CAAA,YAAS,CAAC,aAAa,CAC5C,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,yHAAA,CAAA,iBAAc,CAAC,oBAAoB,EACnC;4BACE,iJAAA,CAAA,QAAK,CAAC,SAAS,CAAC;4BAChB,iJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,GAAG,sBAAsB;yBACtC;wBAEH,YAAY,SAAS,SAAS,CAAC,GAAG;2EAAC,CAAA,MAAO,CAAC;oCACzC,KAAK,IAAI,GAAG;oCACZ,MAAM,IAAI,IAAI;oCACd,OAAO,IAAI,KAAK;oCAChB,aAAa,IAAI,WAAW;oCAC5B,WAAW,IAAI,SAAS;oCACxB,YAAY,IAAI,UAAU;gCAC5B,CAAC;;oBACH,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,mCAAmC;oBACnD,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;kCAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA,OAAQ,CAAC;gBACxB,GAAG,IAAI;gBACP,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,UAAU;YAC/B,CAAC;IACH;IAEA,+BAA+B;IAC/B,MAAM,oBAA8B;QAClC,QAAQ;YACN,SAAS;YACT,GAAG;QACL;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;gBACT,iBAAiB;gBACjB,eAAe;gBACf,MAAM;YACR;QACF;IACF;IAEA,0BAA0B;IAC1B,MAAM,eAAyB;QAC7B,QAAQ;YACN,SAAS;YACT,GAAG;YACH,OAAO;YACP,SAAS;QACX;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,OAAO;YACP,SAAS;YACT,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;gBACT,UAAU;gBACV,OAAO;YACT;QACF;QACA,OAAO;YACL,GAAG,CAAC;YACJ,OAAO;YACP,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;YACA,WAAW;QACb;QACA,KAAK;YACH,OAAO;YACP,GAAG,CAAC;YACJ,WAAW;YACX,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;IAEF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,8IAAA,CAAA,UAAc;gBAAC,MAAK;gBAAQ,MAAK;;;;;;;;;;;IAGxC;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,gCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,8IAAA,CAAA,UAAc;wBAAC,MAAK;wBAAQ,MAAK;;;;;;;;;;;;;;;;YAKvC,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,8IAAA,CAAA,UAAc;wBAAC,MAAK;wBAAQ,MAAK;;;;;;;;;;;;;;;qCAItC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,KAAK;gBACL,WAAU;gBACV,UAAU;gBACV,SAAQ;gBACR,SAAS,kBAAkB,YAAY;0BAEtC,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAM,CAAC,SAAS,EAAE,QAAQ,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,MAAM;wBAEnE,SAAS;4BACP,sBAAsB;4BACtB,kBAAkB;4BAClB,oBAAoB;4BACpB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;4BAE/B,aAAa,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC;gCACrD,MAAM,QAAQ,IAAI;gCAClB,OAAO,QAAQ,KAAK;gCACpB,aAAa,QAAQ,WAAW;gCAChC,WAAW,QAAQ,SAAS;4BAC9B;wBACF;kCAEA,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BAEV,UAAU;4BACV,YAAY;gCAAE,GAAG,CAAC;4BAAE;4BACpB,UAAU;gCAAE,OAAO;4BAAK;4BACxB,OAAO;gCACL,oBAAoB;gCACpB,qBAAqB;4BACvB;;8CAGA,6LAAC;oCAAI,WAAU;;wCACZ,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,kBAC/C,6LAAC,gIAAA,CAAA,UAAK;4CACJ,WAAU;4CACV,IAAI;4CACJ,KAAK,QAAQ,IAAI,IAAI;4CACrB,KAAK,QAAQ,SAAS,CAAC,EAAE;4CACzB,aAAa;4CACb,OAAM;4CACN,OAAO;gDAAE,WAAW;4CAAQ;;;;;iEAG9B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;sDAIjC,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAIjB,6LAAC;oCACC,SAAS,CAAC;wCACR,EAAE,cAAc;wCAChB,EAAE,eAAe;wCACjB,WAAW,QAAQ,GAAG;oCACxB;oCACA,WAAU;8CAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCACJ,WAAU;wCACV,MAAM,aAAa,CAAC,QAAQ,GAAG,CAAC,GAAG,YAAY;wCAC/C,MAAM;wCACN,aAAa;;;;;;;;;;;8CAKjB,6LAAC;oCAAI,WAAU;;sDAGb,6LAAC;4CAAI,WAAU;sDACZ,QAAQ,IAAI;;;;;;sDAEf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;;oDAA4C;oDAAE,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;uBAzEzE,QAAQ,GAAG;;;;;;;;;;0BAmFxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS,kBAAkB;oBAAE,SAAS;oBAAG,GAAG;gBAAE,IAAI;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBACtE,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;gBACxC,WAAU;0BAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAM;oBAAS,SAAS,IAAM,cAAc;8BAChD,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;wBACxB,WAAU;;4BAEX;0CAEC,6LAAC,qNAAA,CAAA,aAAU;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9B;GAxQM;;QACsB,8IAAA,CAAA,gBAAa;QAMgB,sKAAA,CAAA,YAAS;;;KAP5D;uCA0QS"}}, {"offset": {"line": 2128, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2134, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/ClientMap.tsx"], "sourcesContent": ["'use client';\r\nimport dynamic from 'next/dynamic';\r\n\r\nconst Map = dynamic(() => import('./Map'), {\r\n    ssr: false,\r\n    loading: () => <div className=\"w-full h-full bg-gray-100 animate-pulse rounded-lg\" />\r\n});\r\n\r\nexport default Map;\r\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAGA,MAAM,MAAM,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,OAAE;;;;;;IAChB,KAAK;IACL,SAAS,kBAAM,6LAAC;YAAI,WAAU;;;;;;;;uCAGnB"}}, {"offset": {"line": 2165, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2171, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/OpeningHours.tsx"], "sourcesContent": ["\"use client\"\r\nimport Image from \"next/image\";\r\nimport { motion, useAnimation } from \"framer-motion\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { useInView } from \"react-intersection-observer\";\r\nimport ClientMap from './ClientMap';\r\n\r\nconst OpeningHours = () => {\r\n  const controls = useAnimation();\r\n  const [ref, inView] = useInView({\r\n    threshold: 0.2,\r\n  });\r\n  const [isMounted, setIsMounted] = useState(false);\r\n  const [currentDay, setCurrentDay] = useState<string>(\"\");\r\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\r\n\r\n  // Images for the carousel\r\n  const carouselImages = [\r\n    \"/images/sho1.jpg\",\r\n    \"/images/sho2.jpg\",\r\n    \"/images/shop4.jpg\",\r\n    \"/images/shop3.jpg\",\r\n    \"/images/sho3.jpg\"\r\n  ];\r\n\r\n  useEffect(() => {\r\n    setIsMounted(true);\r\n    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];\r\n    const today = days[new Date().getDay()];\r\n    setCurrentDay(today);\r\n\r\n    // Set up image carousel rotation\r\n    const imageInterval = setInterval(() => {\r\n      setCurrentImageIndex(prevIndex => (prevIndex + 1) % carouselImages.length);\r\n    }, 4000);\r\n\r\n    return () => clearInterval(imageInterval);\r\n  }, [carouselImages.length]);\r\n\r\n  useEffect(() => {\r\n    if (inView) {\r\n      controls.start(\"visible\");\r\n    } else {\r\n      controls.start(\"hidden\");\r\n    }\r\n  }, [controls, inView]);\r\n\r\n  const handleGetDirections = () => {\r\n    if (!isMounted || typeof window === 'undefined') return;\r\n    window?.open(`https://www.google.com/maps/search/?api=1&query=6.456559134970387,3.3842979366622847`);\r\n  };\r\n\r\n  const sectionVariant = {\r\n    hidden: { opacity: 0, y: 50 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: { duration: 0.8, ease: \"easeOut\" },\r\n    },\r\n  };\r\n\r\n  const blipVariant = {\r\n    initial: { opacity: 0.3, scale: 1 },\r\n    animate: {\r\n      opacity: [0.3, 1, 0.3],\r\n      scale: [1, 1.4, 1],\r\n      transition: {\r\n        duration: 2,\r\n        repeat: Infinity,\r\n        ease: \"easeInOut\"\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      ref={ref}\r\n      className=\"openin flex flex-row justify-around md:justify-start md:flex-col w-full md:w-[90%] h-auto md:h-[600px] bg-[#EDEDED] rounded-none md:rounded-[30px] py-8 md:py-14 px-4 md:px-20 space-y-10 md:space-y-20\"\r\n      variants={sectionVariant}\r\n      initial=\"hidden\"\r\n      animate={controls}\r\n    >\r\n\r\n      {/* Timeline UI */}\r\n      <motion.div\r\n        className=\"opening-time relative flex  flex-col md:flex-row justify-around space-y-4 md:space-y-0\"\r\n        variants={sectionVariant}\r\n      >\r\n        <div className=\"w-[10px] md:w-[15px] h-[10px] md:h-[15px] bg-[#ff69b400] md:hidden rounded-full flex justify-center\">\r\n          <div className=\"h-[86%] w-[2px] rounded-full bg-[#FF69B4] absolute top-11 md:hidden\"></div>\r\n        </div>\r\n\r\n        {[\r\n          { day: \"Monday\", time: \"08:00-13:00\" },\r\n          { day: \"Tuesday\", time: \"08:00-13:00\" },\r\n          { day: \"Wednesday\", time: \"08:00-13:00\" },\r\n          { day: \"Thursday\", time: \"08:00-13:00\" },\r\n          { day: \"Friday\", time: \"08:00-13:00\" },\r\n          { day: \"Saturday\", time: \"08:00-13:00\" },\r\n          { day: \"Sunday\", time: \"Closed\", isRed: true }\r\n        ].map((item) => (\r\n          <div key={item.day} className=\"flex flex-row md:flex-col items-center space-y-2 md:space-y-4 space-x-4 md:space-x-0\">\r\n            {/* mobile dot */}\r\n            <div className=\"w-[10px] h-[10px] md:hidden bg-[#FF69B4] rounded-full flex items-center justify-center\">\r\n              {currentDay === item.day && (\r\n                <motion.div\r\n                  variants={blipVariant}\r\n                  initial=\"initial\"\r\n                  animate=\"animate\"\r\n                  className=\"absolute w-[16px] h-[16px] bg-[#FF69B4] rounded-full opacity-30\"\r\n                />\r\n              )}\r\n            </div>\r\n\r\n            <div>\r\n              <div className=\"text-sm md:text-xl \">{item.day}</div>\r\n              <div className={`text-[11px] md:text-[15px] ${item.isRed ? 'text-red-400' : ''}`}>{item.time}</div>\r\n            </div>\r\n\r\n            {/* desktop dot */}\r\n            <div className=\"w-[15px] h-[15px] hidden md:flex items-center justify-center bg-[#FF69B4] rounded-full\">\r\n              {currentDay === item.day && (\r\n                <motion.div\r\n                  variants={blipVariant}\r\n                  initial=\"initial\"\r\n                  animate=\"animate\"\r\n                  className=\"absolute w-[24px] h-[24px] bg-[#FF69B4] rounded-full opacity-30\"\r\n                />\r\n              )}\r\n            </div>\r\n          </div>\r\n        ))}\r\n        {/*desktop line */}\r\n        <div className=\"w-[86%] md:h-[3px] bottom-[6px] rounded-full bg-[#FF69B4] absolute\"></div>\r\n        {/*mobile line */}\r\n        {/* <div className=\"h-[90%] w-[2px] rounded-full bg-[#FF69B4] absolute md:hidden\"></div> */}\r\n      </motion.div>\r\n\r\n      <motion.div\r\n        className=\"relative flex justify-start ml-6 md:justify-center md:ml-0\"\r\n        variants={sectionVariant}\r\n      >\r\n        {/* Image mask mobile */}\r\n        <div className=\"w-[200px] h-[180px] md:w-full md:h-[320px] bg-black rounded-[20px] overflow-hidden md:overscroll-none md:relative\">\r\n          {/* Image shop and map */}\r\n          <div className=\"h-full w-full md:absolute md:w-full\">\r\n            {/* Image Carousel */}\r\n            <div className=\"relative h-full w-full overflow-hidden\">\r\n              {carouselImages.map((image, index) => (\r\n                <motion.div\r\n                  key={image}\r\n                  className=\"absolute inset-0\"\r\n                  initial={{ opacity: 0 }}\r\n                  animate={{\r\n                    opacity: index === currentImageIndex ? 1 : 0,\r\n                    scale: index === currentImageIndex ? [1, 1.05] : 1,\r\n                    y: index === currentImageIndex ? ['0%', '-5%'] : '0%'\r\n                  }}\r\n                  transition={{\r\n                    opacity: { duration: 1 },\r\n                    scale: { duration: 8, ease: \"easeInOut\" },\r\n                    y: { duration: 8, ease: \"easeInOut\" }\r\n                  }}\r\n                >\r\n                  <Image\r\n                    className=\"object-cover scale-[1] md:scale-100 object-center\"\r\n                    fill\r\n                    sizes=\"(max-width: 768px) 200px, 100vw\"\r\n                    src={image}\r\n                    alt={`Cepoka shop ${index + 1}`}\r\n                    priority={index === 0}\r\n                  />\r\n                </motion.div>\r\n              ))}\r\n\r\n              {/* Dim fade overlay for transition */}\r\n              <motion.div\r\n                className=\"absolute inset-0 bg-black z-10\"\r\n                initial={{ opacity: 0 }}\r\n                animate={{\r\n                  opacity: [0, 0.3, 0]\r\n                }}\r\n                transition={{\r\n                  duration: 4,\r\n                  ease: \"easeInOut\",\r\n                  repeat: Infinity,\r\n                  repeatDelay: 4\r\n                }}\r\n              />\r\n\r\n              {/* Image fade overlay */}\r\n              <div className=\"hidden md:block top-0 left-0 w-full h-full absolute rounded-[10px] md:rounded-[30px] bg-gradient-to-r from-black to-transparent z-20\"></div>\r\n\r\n              {/* Navigation dots */}\r\n              <div className=\"absolute bottom-4 left-0 right-0 flex justify-center space-x-2 z-30\">\r\n                {carouselImages.map((_, index) => (\r\n                  <button\r\n                    key={index}\r\n                    onClick={() => setCurrentImageIndex(index)}\r\n                    className={`w-2 h-2 rounded-full transition-all duration-300 ${index === currentImageIndex\r\n                      ? 'bg-white scale-110'\r\n                      : 'bg-white/50 hover:bg-white/70'\r\n                      }`}\r\n                    aria-label={`Go to slide ${index + 1}`}\r\n                  />\r\n                ))}\r\n              </div>\r\n            </div>\r\n            {/*Map */}\r\n            <div className=\"absolute flex flex-col top-[200px] md:top-[25%] left-0 md:left-[10%] space-y-2 z-40\">\r\n              <div className=\"relative w-[200px] md:w-[250px] border-2 border-[#333333] rounded-lg md:border-0\">\r\n                <ClientMap height=\"150px\" />\r\n              </div>\r\n              <motion.button\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.9 }}\r\n                style={{ backdropFilter: \"blur(.2em)\" }}\r\n                className=\"flex space-x-2 md:space-x-4 items-center justify-center w-[200px] md:w-[70%] text-white text-sm bg-[#333333] md:bg-[#55555558] rounded-full py-3 md:p-2 border-[1.5px] border-[#fff] md:border-[#5b5b5b]\"\r\n                onClick={handleGetDirections}\r\n              >\r\n                <div className=\"\">Get Directions</div>\r\n                <Image\r\n                  width={20}\r\n                  height={20}\r\n                  src={\"/icons/location.png\"}\r\n                  alt=\"\"\r\n                />\r\n              </motion.button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </motion.div>\r\n\r\n    </motion.div>\r\n  );\r\n};\r\n\r\nexport default OpeningHours;\r\n"], "names": [], "mappings": ";;;;AACA;AAEA;AAEA;AAHA;AAEA;AAFA;;;AAFA;;;;;;AAOA,MAAM,eAAe;;IACnB,MAAM,WAAW,CAAA,GAAA,4LAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,WAAW;IACb;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,0BAA0B;IAC1B,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,aAAa;YACb,MAAM,OAAO;gBAAC;gBAAU;gBAAU;gBAAW;gBAAa;gBAAY;gBAAU;aAAW;YAC3F,MAAM,QAAQ,IAAI,CAAC,IAAI,OAAO,MAAM,GAAG;YACvC,cAAc;YAEd,iCAAiC;YACjC,MAAM,gBAAgB;wDAAY;oBAChC;gEAAqB,CAAA,YAAa,CAAC,YAAY,CAAC,IAAI,eAAe,MAAM;;gBAC3E;uDAAG;YAEH;0CAAO,IAAM,cAAc;;QAC7B;iCAAG;QAAC,eAAe,MAAM;KAAC;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,QAAQ;gBACV,SAAS,KAAK,CAAC;YACjB,OAAO;gBACL,SAAS,KAAK,CAAC;YACjB;QACF;iCAAG;QAAC;QAAU;KAAO;IAErB,MAAM,sBAAsB;QAC1B,IAAI,CAAC,aAAa,aAAkB,aAAa;QACjD,QAAQ,KAAK,CAAC,oFAAoF,CAAC;IACrG;IAEA,MAAM,iBAAiB;QACrB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;QAC/C;IACF;IAEA,MAAM,cAAc;QAClB,SAAS;YAAE,SAAS;YAAK,OAAO;QAAE;QAClC,SAAS;YACP,SAAS;gBAAC;gBAAK;gBAAG;aAAI;YACtB,OAAO;gBAAC;gBAAG;gBAAK;aAAE;YAClB,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;YACR;QACF;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAU;QACV,UAAU;QACV,SAAQ;QACR,SAAS;;0BAIT,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;;kCAEV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;oBAGhB;wBACC;4BAAE,KAAK;4BAAU,MAAM;wBAAc;wBACrC;4BAAE,KAAK;4BAAW,MAAM;wBAAc;wBACtC;4BAAE,KAAK;4BAAa,MAAM;wBAAc;wBACxC;4BAAE,KAAK;4BAAY,MAAM;wBAAc;wBACvC;4BAAE,KAAK;4BAAU,MAAM;wBAAc;wBACrC;4BAAE,KAAK;4BAAY,MAAM;wBAAc;wBACvC;4BAAE,KAAK;4BAAU,MAAM;4BAAU,OAAO;wBAAK;qBAC9C,CAAC,GAAG,CAAC,CAAC,qBACL,6LAAC;4BAAmB,WAAU;;8CAE5B,6LAAC;oCAAI,WAAU;8CACZ,eAAe,KAAK,GAAG,kBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,UAAU;wCACV,SAAQ;wCACR,SAAQ;wCACR,WAAU;;;;;;;;;;;8CAKhB,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAuB,KAAK,GAAG;;;;;;sDAC9C,6LAAC;4CAAI,WAAW,CAAC,2BAA2B,EAAE,KAAK,KAAK,GAAG,iBAAiB,IAAI;sDAAG,KAAK,IAAI;;;;;;;;;;;;8CAI9F,6LAAC;oCAAI,WAAU;8CACZ,eAAe,KAAK,GAAG,kBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,UAAU;wCACV,SAAQ;wCACR,SAAQ;wCACR,WAAU;;;;;;;;;;;;2BAzBR,KAAK,GAAG;;;;;kCAgCpB,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAKjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;0BAGV,cAAA,6LAAC;oBAAI,WAAU;8BAEb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;oCACZ,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,WAAU;4CACV,SAAS;gDAAE,SAAS;4CAAE;4CACtB,SAAS;gDACP,SAAS,UAAU,oBAAoB,IAAI;gDAC3C,OAAO,UAAU,oBAAoB;oDAAC;oDAAG;iDAAK,GAAG;gDACjD,GAAG,UAAU,oBAAoB;oDAAC;oDAAM;iDAAM,GAAG;4CACnD;4CACA,YAAY;gDACV,SAAS;oDAAE,UAAU;gDAAE;gDACvB,OAAO;oDAAE,UAAU;oDAAG,MAAM;gDAAY;gDACxC,GAAG;oDAAE,UAAU;oDAAG,MAAM;gDAAY;4CACtC;sDAEA,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDACJ,WAAU;gDACV,IAAI;gDACJ,OAAM;gDACN,KAAK;gDACL,KAAK,CAAC,YAAY,EAAE,QAAQ,GAAG;gDAC/B,UAAU,UAAU;;;;;;2CApBjB;;;;;kDA0BT,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CACP,SAAS;gDAAC;gDAAG;gDAAK;6CAAE;wCACtB;wCACA,YAAY;4CACV,UAAU;4CACV,MAAM;4CACN,QAAQ;4CACR,aAAa;wCACf;;;;;;kDAIF,6LAAC;wCAAI,WAAU;;;;;;kDAGf,6LAAC;wCAAI,WAAU;kDACZ,eAAe,GAAG,CAAC,CAAC,GAAG,sBACtB,6LAAC;gDAEC,SAAS,IAAM,qBAAqB;gDACpC,WAAW,CAAC,iDAAiD,EAAE,UAAU,oBACrE,uBACA,iCACA;gDACJ,cAAY,CAAC,YAAY,EAAE,QAAQ,GAAG;+CANjC;;;;;;;;;;;;;;;;0CAYb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yIAAA,CAAA,UAAS;4CAAC,QAAO;;;;;;;;;;;kDAEpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAI;wCACvB,OAAO;4CAAE,gBAAgB;wCAAa;wCACtC,WAAU;wCACV,SAAS;;0DAET,6LAAC;gDAAI,WAAU;0DAAG;;;;;;0DAClB,6LAAC,gIAAA,CAAA,UAAK;gDACJ,OAAO;gDACP,QAAQ;gDACR,KAAK;gDACL,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtB;GApOM;;QACa,4LAAA,CAAA,eAAY;QACP,sKAAA,CAAA,YAAS;;;KAF3B;uCAsOS"}}, {"offset": {"line": 2628, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2634, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/data/categories.ts"], "sourcesContent": ["export interface Category {\r\n  id: string;\r\n  name: string;\r\n  icon: string;\r\n  imageSrc: string;\r\n}\r\n\r\nexport const CATEGORIES: Category[] = [\r\n  {\r\n    id: \"spa-salon-furniture\",\r\n    name: \"Spa and salon furnitures\",\r\n    icon: \"🪑\",\r\n    imageSrc: \"/icons/spa-bed.png\",\r\n  },\r\n  {\r\n    id: \"beauty-equipment\",\r\n    name: \"Beauty equipment\",\r\n    icon: \"⚙️\",\r\n    imageSrc: \"/icons/hairdryer.png\",\r\n  },\r\n  {\r\n    id: \"facial-waxing\",\r\n    name: \"Facials and waxing\",\r\n    icon: \"🧖‍♀️\",\r\n    imageSrc: \"/icons/hot-stone.png\",\r\n  },\r\n  {\r\n    id: \"skincare-accessories\",\r\n    name: \"Skincare products & accessories\",\r\n    icon: \"🧴\",\r\n    imageSrc: \"/icons/slim.png\",\r\n  },\r\n  {\r\n    id: \"pedicure-manicure\",\r\n    name: \"Pedicure and manicure\",\r\n    icon: \"💅\",\r\n    imageSrc: \"/icons/nails.png\",\r\n  },\r\n];\r\n"], "names": [], "mappings": ";;;AAOO,MAAM,aAAyB;IACpC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;IACZ;CACD"}}, {"offset": {"line": 2672, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2678, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/HomeCategories.tsx"], "sourcesContent": ["\"use client\"\r\nimport Image from \"next/image\";\r\nimport { motion } from \"framer-motion\";\r\nimport { useInView } from \"react-intersection-observer\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { CATEGORIES } from '@/src/data/categories';\r\n\r\n// Props interface for individual category items\r\ninterface CategoryItemProps {\r\n    imageSrc: string;    // Path to category image\r\n    label: string;       // Category display name\r\n    categoryId: string;  // Unique identifier for category\r\n    index: number;      // Position in the grid for animation sequencing\r\n    isCenter: boolean; // Whether this is the center item (for special styling)\r\n    position: 'left' | 'right' | 'center'; // Item position for layout\r\n}\r\n\r\n// Main Categories component - Renders a custom layout with 5 categories\r\nconst HomeCategories = () => {\r\n    // Only use the first 5 categories if there are more\r\n    const displayCategories = CATEGORIES.slice(0, 5);\r\n\r\n    // Rearrange categories to have Beauty Equipment in the center\r\n    // Find Beauty Equipment category\r\n    const beautyEquipmentIndex = displayCategories.findIndex(cat => cat.id === \"beauty-equipment\");\r\n\r\n    // If Beauty Equipment is found, move it to the center (index 2)\r\n    const arrangedCategories = [...displayCategories];\r\n    if (beautyEquipmentIndex !== -1 && beautyEquipmentIndex !== 2) {\r\n        // Remove Beauty Equipment from its current position\r\n        const beautyEquipment = arrangedCategories.splice(beautyEquipmentIndex, 1)[0];\r\n        // Insert it at position 2 (center)\r\n        arrangedCategories.splice(2, 0, beautyEquipment);\r\n    }\r\n\r\n    return (\r\n        <div className=\"categories px-4 py-12 sm:px-6 md:px-8 lg:px-12\">\r\n            {/* Desktop layout */}\r\n            <div className=\"hidden md:flex justify-center items-center min-h-[500px] relative\">\r\n                <div className=\"flex justify-center items-center w-full max-w-7xl mx-auto px-8\">\r\n                    {/* Left column */}\r\n                    <div className=\"flex flex-col gap-16 mr-16\"> {/* Increased gap and margin */}\r\n                        <CategoryItem\r\n                            key={arrangedCategories[0].id}\r\n                            imageSrc={arrangedCategories[0].imageSrc}\r\n                            label={arrangedCategories[0].name}\r\n                            categoryId={arrangedCategories[0].id}\r\n                            index={0}\r\n                            isCenter={false}\r\n                            position=\"left\"\r\n                        />\r\n                        <CategoryItem\r\n                            key={arrangedCategories[1].id}\r\n                            imageSrc={arrangedCategories[1].imageSrc}\r\n                            label={arrangedCategories[1].name}\r\n                            categoryId={arrangedCategories[1].id}\r\n                            index={1}\r\n                            isCenter={false}\r\n                            position=\"left\"\r\n                        />\r\n                    </div>\r\n\r\n                    {/* Center item */}\r\n                    <div className=\"transform scale-110 z-10\"> {/* Reduced scale from 125 to 110 */}\r\n                        <CategoryItem\r\n                            key={arrangedCategories[2].id}\r\n                            imageSrc={arrangedCategories[2].imageSrc}\r\n                            label={arrangedCategories[2].name}\r\n                            categoryId={arrangedCategories[2].id}\r\n                            index={2}\r\n                            isCenter={true}\r\n                            position=\"center\"\r\n                        />\r\n                    </div>\r\n\r\n                    {/* Right column */}\r\n                    <div className=\"flex flex-col gap-16 ml-16\"> {/* Increased gap and margin */}\r\n                        <CategoryItem\r\n                            key={arrangedCategories[3].id}\r\n                            imageSrc={arrangedCategories[3].imageSrc}\r\n                            label={arrangedCategories[3].name}\r\n                            categoryId={arrangedCategories[3].id}\r\n                            index={3}\r\n                            isCenter={false}\r\n                            position=\"right\"\r\n                        />\r\n                        <CategoryItem\r\n                            key={arrangedCategories[4].id}\r\n                            imageSrc={arrangedCategories[4].imageSrc}\r\n                            label={arrangedCategories[4].name}\r\n                            categoryId={arrangedCategories[4].id}\r\n                            index={4}\r\n                            isCenter={false}\r\n                            position=\"right\"\r\n                        />\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Mobile layout */}\r\n            <div className=\"md:hidden space-y-8\">\r\n                {/* Center item first */}\r\n                <div className=\"flex justify-center\">\r\n                    <CategoryItem\r\n                        key={arrangedCategories[2].id}\r\n                        imageSrc={arrangedCategories[2].imageSrc}\r\n                        label={arrangedCategories[2].name}\r\n                        categoryId={arrangedCategories[2].id}\r\n                        index={2}\r\n                        isCenter={true}\r\n                        position=\"center\"\r\n                    />\r\n                </div>\r\n\r\n                {/* 2x2 grid for other items */}\r\n                <div className=\"grid grid-cols-2 gap-6\">\r\n                    {[0, 1, 3, 4].map((index) => (\r\n                        <div key={arrangedCategories[index].id} className=\"flex justify-center\">\r\n                            <CategoryItem\r\n                                imageSrc={arrangedCategories[index].imageSrc}\r\n                                label={arrangedCategories[index].name}\r\n                                categoryId={arrangedCategories[index].id}\r\n                                index={index}\r\n                                isCenter={false}\r\n                                position={index < 2 ? \"left\" : \"right\"}\r\n                            />\r\n                        </div>\r\n                    ))}\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\n// Individual category item component with animations\r\nconst CategoryItem: React.FC<CategoryItemProps> = ({ imageSrc, label, categoryId, index, isCenter, position }) => {\r\n    const router = useRouter();\r\n\r\n    // Navigation handler for category clicks\r\n    const handleCategoryClick = () => {\r\n        // Route to shop page with the category parameter\r\n        // The categoryId will be from categories.ts\r\n        router.push(`/shop?category=${categoryId}&select=true`);\r\n    };\r\n\r\n    // Set up intersection observer to trigger animations when item becomes visible\r\n    const { ref, inView } = useInView({\r\n        triggerOnce: true,    // Animation plays only once\r\n        threshold: 0.1,       // Trigger when 10% of item is visible\r\n        rootMargin: \"50px\",   // Start animation slightly before item enters viewport\r\n    });\r\n\r\n    // Animations based on position\r\n    const getAnimationVariants = () => {\r\n        if (position === \"center\") {\r\n            return {\r\n                hidden: { opacity: 0, y: 20, scale: 0.9 },\r\n                visible: {\r\n                    opacity: 1,\r\n                    y: 0,\r\n                    scale: 1,\r\n                    transition: { duration: 0.6, ease: \"easeOut\" }\r\n                }\r\n            };\r\n        }\r\n\r\n        const xOffset = position === \"left\" ? -30 : 30;\r\n        return {\r\n            hidden: { opacity: 0, x: xOffset },\r\n            visible: {\r\n                opacity: 1,\r\n                x: 0,\r\n                transition: { duration: 0.5, ease: \"easeOut\" }\r\n            }\r\n        };\r\n    };\r\n\r\n    const variants = getAnimationVariants();\r\n\r\n    // Calculate sizes based on position\r\n    const getContainerStyles = () => {\r\n        if (isCenter) {\r\n            return \"w-[160px] h-[160px] sm:w-[200px] sm:h-[200px] md:w-[220px] md:h-[220px]\"; // Reduced size for center item\r\n        }\r\n        return \"w-[130px] h-[130px] sm:w-[150px] sm:h-[150px] md:w-[170px] md:h-[170px]\";\r\n    };\r\n\r\n    return (\r\n        // Animated container for category item\r\n        <motion.div\r\n            ref={ref}\r\n            variants={variants}\r\n            initial=\"hidden\"\r\n            animate={inView ? \"visible\" : \"hidden\"}\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            onClick={handleCategoryClick}\r\n            className=\"flex flex-col items-center cursor-pointer group\"\r\n        >\r\n            {/* Animated image container with hover and tap effects */}\r\n            <div className={`relative ${getContainerStyles()} bg-gradient-to-b from-gray-300 to-gray-300 rounded-full overflow-visible shadow-lg hover:shadow-xl transition-all duration-300`}>\r\n                {/* <div className=\"absolute inset-0 bg-gray-100 rounded-full transform -rotate-6 scale-95 opacity-50\" />\r\n                <div className=\"absolute inset-0 bg-gray-100 rounded-full transform rotate-3 scale-95 opacity-50\" /> */}\r\n                <div className=\"relative w-full h-full rounded-full overflow-hidden bg-balck\">\r\n                    <Image\r\n                        src={imageSrc}\r\n                        alt={label}\r\n                        fill\r\n                        className=\"object-contain p-6 transition-transform duration-300 group-hover:scale-110\"\r\n                    />\r\n                </div>\r\n            </div>\r\n            {/* Category label */}\r\n            <h3 className={`mt-4 text-center font-medium ${isCenter ? 'text-lg sm:text-xl' : 'text-base sm:text-lg'}`}>\r\n                {label}\r\n            </h3>\r\n        </motion.div>\r\n    );\r\n}\r\n\r\nexport default HomeCategories;\r\n"], "names": [], "mappings": ";;;;AACA;AAGA;AACA;AAFA;AADA;;;AAFA;;;;;;AAiBA,wEAAwE;AACxE,MAAM,iBAAiB;IACnB,oDAAoD;IACpD,MAAM,oBAAoB,4HAAA,CAAA,aAAU,CAAC,KAAK,CAAC,GAAG;IAE9C,8DAA8D;IAC9D,iCAAiC;IACjC,MAAM,uBAAuB,kBAAkB,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;IAE3E,gEAAgE;IAChE,MAAM,qBAAqB;WAAI;KAAkB;IACjD,IAAI,yBAAyB,CAAC,KAAK,yBAAyB,GAAG;QAC3D,oDAAoD;QACpD,MAAM,kBAAkB,mBAAmB,MAAM,CAAC,sBAAsB,EAAE,CAAC,EAAE;QAC7E,mCAAmC;QACnC,mBAAmB,MAAM,CAAC,GAAG,GAAG;IACpC;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BAEX,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;;sCAEX,6LAAC;4BAAI,WAAU;;gCAA6B;8CACxC,6LAAC;oCAEG,UAAU,kBAAkB,CAAC,EAAE,CAAC,QAAQ;oCACxC,OAAO,kBAAkB,CAAC,EAAE,CAAC,IAAI;oCACjC,YAAY,kBAAkB,CAAC,EAAE,CAAC,EAAE;oCACpC,OAAO;oCACP,UAAU;oCACV,UAAS;mCANJ,kBAAkB,CAAC,EAAE,CAAC,EAAE;;;;;8CAQjC,6LAAC;oCAEG,UAAU,kBAAkB,CAAC,EAAE,CAAC,QAAQ;oCACxC,OAAO,kBAAkB,CAAC,EAAE,CAAC,IAAI;oCACjC,YAAY,kBAAkB,CAAC,EAAE,CAAC,EAAE;oCACpC,OAAO;oCACP,UAAU;oCACV,UAAS;mCANJ,kBAAkB,CAAC,EAAE,CAAC,EAAE;;;;;;;;;;;sCAWrC,6LAAC;4BAAI,WAAU;;gCAA2B;8CACtC,6LAAC;oCAEG,UAAU,kBAAkB,CAAC,EAAE,CAAC,QAAQ;oCACxC,OAAO,kBAAkB,CAAC,EAAE,CAAC,IAAI;oCACjC,YAAY,kBAAkB,CAAC,EAAE,CAAC,EAAE;oCACpC,OAAO;oCACP,UAAU;oCACV,UAAS;mCANJ,kBAAkB,CAAC,EAAE,CAAC,EAAE;;;;;;;;;;;sCAWrC,6LAAC;4BAAI,WAAU;;gCAA6B;8CACxC,6LAAC;oCAEG,UAAU,kBAAkB,CAAC,EAAE,CAAC,QAAQ;oCACxC,OAAO,kBAAkB,CAAC,EAAE,CAAC,IAAI;oCACjC,YAAY,kBAAkB,CAAC,EAAE,CAAC,EAAE;oCACpC,OAAO;oCACP,UAAU;oCACV,UAAS;mCANJ,kBAAkB,CAAC,EAAE,CAAC,EAAE;;;;;8CAQjC,6LAAC;oCAEG,UAAU,kBAAkB,CAAC,EAAE,CAAC,QAAQ;oCACxC,OAAO,kBAAkB,CAAC,EAAE,CAAC,IAAI;oCACjC,YAAY,kBAAkB,CAAC,EAAE,CAAC,EAAE;oCACpC,OAAO;oCACP,UAAU;oCACV,UAAS;mCANJ,kBAAkB,CAAC,EAAE,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;0BAa7C,6LAAC;gBAAI,WAAU;;kCAEX,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAEG,UAAU,kBAAkB,CAAC,EAAE,CAAC,QAAQ;4BACxC,OAAO,kBAAkB,CAAC,EAAE,CAAC,IAAI;4BACjC,YAAY,kBAAkB,CAAC,EAAE,CAAC,EAAE;4BACpC,OAAO;4BACP,UAAU;4BACV,UAAS;2BANJ,kBAAkB,CAAC,EAAE,CAAC,EAAE;;;;;;;;;;kCAWrC,6LAAC;wBAAI,WAAU;kCACV;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,sBACf,6LAAC;gCAAuC,WAAU;0CAC9C,cAAA,6LAAC;oCACG,UAAU,kBAAkB,CAAC,MAAM,CAAC,QAAQ;oCAC5C,OAAO,kBAAkB,CAAC,MAAM,CAAC,IAAI;oCACrC,YAAY,kBAAkB,CAAC,MAAM,CAAC,EAAE;oCACxC,OAAO;oCACP,UAAU;oCACV,UAAU,QAAQ,IAAI,SAAS;;;;;;+BAP7B,kBAAkB,CAAC,MAAM,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;AAe9D;KAlHM;AAoHN,qDAAqD;AACrD,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE;;IACzG,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,yCAAyC;IACzC,MAAM,sBAAsB;QACxB,iDAAiD;QACjD,4CAA4C;QAC5C,OAAO,IAAI,CAAC,CAAC,eAAe,EAAE,WAAW,YAAY,CAAC;IAC1D;IAEA,+EAA+E;IAC/E,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;QACX,YAAY;IAChB;IAEA,+BAA+B;IAC/B,MAAM,uBAAuB;QACzB,IAAI,aAAa,UAAU;YACvB,OAAO;gBACH,QAAQ;oBAAE,SAAS;oBAAG,GAAG;oBAAI,OAAO;gBAAI;gBACxC,SAAS;oBACL,SAAS;oBACT,GAAG;oBACH,OAAO;oBACP,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;gBACjD;YACJ;QACJ;QAEA,MAAM,UAAU,aAAa,SAAS,CAAC,KAAK;QAC5C,OAAO;YACH,QAAQ;gBAAE,SAAS;gBAAG,GAAG;YAAQ;YACjC,SAAS;gBACL,SAAS;gBACT,GAAG;gBACH,YAAY;oBAAE,UAAU;oBAAK,MAAM;gBAAU;YACjD;QACJ;IACJ;IAEA,MAAM,WAAW;IAEjB,oCAAoC;IACpC,MAAM,qBAAqB;QACvB,IAAI,UAAU;YACV,OAAO,2EAA2E,+BAA+B;QACrH;QACA,OAAO;IACX;IAEA,OACI,uCAAuC;kBACvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACP,KAAK;QACL,UAAU;QACV,SAAQ;QACR,SAAS,SAAS,YAAY;QAC9B,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,SAAS;QACT,WAAU;;0BAGV,6LAAC;gBAAI,WAAW,CAAC,SAAS,EAAE,qBAAqB,+HAA+H,CAAC;0BAG7K,cAAA,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC,gIAAA,CAAA,UAAK;wBACF,KAAK;wBACL,KAAK;wBACL,IAAI;wBACJ,WAAU;;;;;;;;;;;;;;;;0BAKtB,6LAAC;gBAAG,WAAW,CAAC,6BAA6B,EAAE,WAAW,uBAAuB,wBAAwB;0BACpG;;;;;;;;;;;;AAIjB;GAnFM;;QACa,qIAAA,CAAA,YAAS;QAUA,sKAAA,CAAA,YAAS;;;MAX/B;uCAqFS"}}, {"offset": {"line": 3016, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3022, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/CustomerReviewData.tsx"], "sourcesContent": ["const reviewData = [\r\n    {\r\n        name: \"<PERSON><PERSON>\",\r\n        quote: \"DFugo Hair's quality and service is truly exceptional. \\nNever felt more confident with my look!\",\r\n        img: \"/images/customer1.jpg\",\r\n    },\r\n    {\r\n        name: \"<PERSON><PERSON>\",\r\n        quote: \"Their wig craftsmanship is simply remarkable. \\nBeen a loyal customer for 3 years for good reason!\",\r\n        img: \"/images/customer2.jpg\",\r\n    },\r\n    {\r\n        name: \"<PERSON><PERSON><PERSON>\",\r\n        quote: \"Best wig investment ever! \\nPremium quality and looks completely natural.\",\r\n        img: \"/images/customer3.png\",\r\n    },\r\n    {\r\n        name: \"Bless<PERSON>\",\r\n        quote: \"These wigs are durable and versatile. \\nMy absolute go-to for luxury wigs.\",\r\n        img: \"/images/customer4.jpg\",\r\n    },\r\n];\r\n\r\nexport default reviewData;\r\n"], "names": [], "mappings": ";;;AAAA,MAAM,aAAa;IACf;QACI,MAAM;QACN,OAAO;QACP,KAAK;IACT;IACA;QACI,MAAM;QACN,OAAO;QACP,KAAK;IACT;IACA;QACI,MAAM;QACN,OAAO;QACP,KAAK;IACT;IACA;QACI,MAAM;QACN,OAAO;QACP,KAAK;IACT;CACH;uCAEc"}}, {"offset": {"line": 3051, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3057, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/Review.tsx"], "sourcesContent": ["\"use client\";\r\nimport Image from \"next/image\";\r\nimport reviewData from \"../CustomerReviewData\";\r\nimport { motion } from \"framer-motion\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\nconst Review = () => {\r\n    const [isMounted, setIsMounted] = useState(false);\r\n    const [windowWidth, setWindowWidth] = useState(0); // Initialize to 0\r\n\r\n    useEffect(() => {\r\n        setIsMounted(true);\r\n        if (typeof window !== 'undefined') {\r\n            setWindowWidth(window.innerWidth);\r\n        }\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        if (!isMounted || typeof window === 'undefined') return;\r\n\r\n        const handleResize = () => {\r\n            setWindowWidth(window.innerWidth);\r\n        };\r\n\r\n        window.addEventListener('resize', handleResize);\r\n        return () => window.removeEventListener('resize', handleResize);\r\n    }, [isMounted]);\r\n\r\n    const getInitialX = (index: number, isMobile: boolean) => {\r\n        if (!isMobile) return index % 2 === 0 ? -30 : 30;\r\n\r\n        // For mobile: indexes 0,2 come from right, 1,3 come from left\r\n        return index % 2 === 0 ? 100 : -100;\r\n    };\r\n\r\n    return (\r\n        <div className=\"review grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-32 md:justify-items-end\">\r\n            {reviewData.map((review, index) => (\r\n                <motion.div\r\n                    key={index}\r\n                    className={`flex items-center relative ${index % 2 === 0 ? 'pl-8 md:pr-0' : 'pr-8 md:pr-0'}`}\r\n                    initial={{\r\n                        opacity: 0,\r\n                        x: getInitialX(index, windowWidth < 768),\r\n                        y: windowWidth >= 768 ? 50 : 0\r\n                    }}\r\n                    whileInView={{\r\n                        opacity: 1,\r\n                        x: 0,\r\n                        y: 0\r\n                    }}\r\n                    transition={{\r\n                        duration: 0.8,\r\n                        type: \"spring\",\r\n                        damping: 25,\r\n                        stiffness: 120,\r\n                        delay: windowWidth < 768 ? index * 0.15 : index * 0.2\r\n                    }}\r\n                    viewport={{ once: true, margin: \"-100px\" }}\r\n                >\r\n                    {/* Masked Image and Text Container */}\r\n                    <div className={`flex items-center relative bg-[#b6b6b6] md:rounded-[30px] overflow-hidden w-full md:w-[430px] h-[150px] md:h-[230px] \r\n            ${index % 2 === 1 ? 'flex-row-reverse md:!flex-row' : ''} \r\n            ${index % 2 === 0 ? 'rounded-r-none rounded-l-[20px] md:rounded-[30px]' : 'rounded-l-none rounded-r-[20px] md:rounded-[30px]'}`}>\r\n                        {/* Image Mask */}\r\n                        <div className={`w-[40px] h-full from-[#b6b6b6] to-transparent z-10 absolute\r\n              ${index % 2 === 0\r\n                                ? 'bg-gradient-to-l left-[35%] md:left-[37%]'\r\n                                : 'bg-gradient-to-r right-[35%] md:left-[37%] md:bg-gradient-to-l'\r\n                            }`}>\r\n                        </div>\r\n                        <div className=\"relative w-[45%] h-full\">\r\n                            <Image\r\n                                src={review.img}\r\n                                alt=\"customer pic\"\r\n                                width={200}\r\n                                height={200}\r\n                                className=\"object-cover w-full h-full\"\r\n                            />\r\n                        </div>\r\n                        {/* Text Section */}\r\n                        <div className={`flex flex-col justify-center py-4 px-5 md:p-6 w-[55%] text-white \r\n              ${index % 2 === 1 ? 'text-right md:!text-left' : 'text-left'}`}>\r\n                            <p className=\"text-[13px] leading-tight md:text-base font-normal\">{review.quote}</p>\r\n                            <p className={`mt-3 md:mt-4 text-xs md:text-base ${index % 2 === 0 ? 'text-right' : 'text-right md:!text-right'}`}>~ {review.name}</p>\r\n                        </div>\r\n                    </div>\r\n                </motion.div>\r\n            ))}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Review;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAEA;AADA;;;AAHA;;;;;AAMA,MAAM,SAAS;;IACX,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,kBAAkB;IAErE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,aAAa;YACb,wCAAmC;gBAC/B,eAAe,OAAO,UAAU;YACpC;QACJ;2BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,IAAI,CAAC,aAAa,aAAkB,aAAa;YAEjD,MAAM;iDAAe;oBACjB,eAAe,OAAO,UAAU;gBACpC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACtD;2BAAG;QAAC;KAAU;IAEd,MAAM,cAAc,CAAC,OAAe;QAChC,IAAI,CAAC,UAAU,OAAO,QAAQ,MAAM,IAAI,CAAC,KAAK;QAE9C,8DAA8D;QAC9D,OAAO,QAAQ,MAAM,IAAI,MAAM,CAAC;IACpC;IAEA,qBACI,6LAAC;QAAI,WAAU;kBACV,oIAAA,CAAA,UAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAEP,WAAW,CAAC,2BAA2B,EAAE,QAAQ,MAAM,IAAI,iBAAiB,gBAAgB;gBAC5F,SAAS;oBACL,SAAS;oBACT,GAAG,YAAY,OAAO,cAAc;oBACpC,GAAG,eAAe,MAAM,KAAK;gBACjC;gBACA,aAAa;oBACT,SAAS;oBACT,GAAG;oBACH,GAAG;gBACP;gBACA,YAAY;oBACR,UAAU;oBACV,MAAM;oBACN,SAAS;oBACT,WAAW;oBACX,OAAO,cAAc,MAAM,QAAQ,OAAO,QAAQ;gBACtD;gBACA,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAS;0BAGzC,cAAA,6LAAC;oBAAI,WAAW,CAAC;YACzB,EAAE,QAAQ,MAAM,IAAI,kCAAkC,GAAG;YACzD,EAAE,QAAQ,MAAM,IAAI,sDAAsD,qDAAqD;;sCAEnH,6LAAC;4BAAI,WAAW,CAAC;cAC3B,EAAE,QAAQ,MAAM,IACI,8CACA,kEACJ;;;;;;sCAEN,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACF,KAAK,OAAO,GAAG;gCACf,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAIlB,6LAAC;4BAAI,WAAW,CAAC;cAC3B,EAAE,QAAQ,MAAM,IAAI,6BAA6B,aAAa;;8CAChD,6LAAC;oCAAE,WAAU;8CAAsD,OAAO,KAAK;;;;;;8CAC/E,6LAAC;oCAAE,WAAW,CAAC,kCAAkC,EAAE,QAAQ,MAAM,IAAI,eAAe,6BAA6B;;wCAAE;wCAAG,OAAO,IAAI;;;;;;;;;;;;;;;;;;;eA7CpI;;;;;;;;;;AAoDzB;GArFM;KAAA;uCAuFS"}}, {"offset": {"line": 3215, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3221, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/AboutFugo.tsx"], "sourcesContent": ["\"use client\";\r\nimport Image from \"next/image\";\r\nimport { motion } from \"framer-motion\";\r\nimport { useState, useEffect } from \"react\";\r\n\r\nconst AboutFugo = () => {\r\n    const [isExpanded, setIsExpanded] = useState(false);\r\n    const [currentImageIndex, setCurrentImageIndex] = useState(0);\r\n\r\n    // Images for the carousel\r\n    const carouselImages = [\r\n        \"/images/ceo2.jpg\",\r\n        \"/images/ceo3.jpg\",\r\n        \"/images/sho1.jpg\",\r\n        \"/images/sho2.jpg\",\r\n        \"/images/sho3.jpg\",\r\n        \"/images/ceo1.jpg\"\r\n    ];\r\n\r\n    // Set up image carousel rotation\r\n    useEffect(() => {\r\n        const imageInterval = setInterval(() => {\r\n            setCurrentImageIndex(prevIndex => (prevIndex + 1) % carouselImages.length);\r\n        }, 4000);\r\n\r\n        return () => clearInterval(imageInterval);\r\n    }, [carouselImages.length]);\r\n\r\n    // IDEA: For better visibility of the showroom and shop images and videos make the image fill on its on and the text should stand apart with its own Bg blending in with the img/vid displaying.\r\n\r\n    return (\r\n        <div className=\"p-0 sm:p-10 w-full h-auto sm:h-[650px] flex justify-center items-center\">\r\n            <motion.div\r\n                className=\"relative w-full sm:w-[80%] h-[500px] sm:h-[80%] overflow-hidden sm:rounded-[30px]\"\r\n                initial={{ opacity: 0, scale: 0.9 }}\r\n                whileInView={{ opacity: 1, scale: 1 }}\r\n                transition={{ duration: 0.6 }}\r\n                viewport={{ once: true }}\r\n            >\r\n                {/* Image Carousel */}\r\n                <div className=\"relative w-full h-full overflow-hidden\">\r\n                    {carouselImages.map((image, index) => (\r\n                        <motion.div\r\n                            key={image}\r\n                            className=\"absolute inset-0\"\r\n                            initial={{ opacity: 0 }}\r\n                            animate={{\r\n                                opacity: index === currentImageIndex ? 1 : 0,\r\n                                scale: index === currentImageIndex ? [1, 1.05] : 1,\r\n                                y: index === currentImageIndex ? ['0%', '-5%'] : '0%'\r\n                            }}\r\n                            transition={{\r\n                                opacity: { duration: 1 },\r\n                                scale: { duration: 8, ease: \"easeInOut\" },\r\n                                y: { duration: 8, ease: \"easeInOut\" }\r\n                            }}\r\n                        >\r\n                            <Image\r\n                                className=\"w-full h-full object-cover\"\r\n                                fill\r\n                                sizes=\"100vw\"\r\n                                src={image}\r\n                                alt={`Cepoka image ${index + 1}`}\r\n                                priority={index === 0}\r\n                            />\r\n                        </motion.div>\r\n                    ))}\r\n\r\n                    {/* Dim fade overlay for transition */}\r\n                    <motion.div\r\n                        className=\"absolute inset-0 bg-black z-10\"\r\n                        initial={{ opacity: 0 }}\r\n                        animate={{\r\n                            opacity: [0, 0.3, 0]\r\n                        }}\r\n                        transition={{\r\n                            duration: 4,\r\n                            ease: \"easeInOut\",\r\n                            repeat: Infinity,\r\n                            repeatDelay: 4\r\n                        }}\r\n                    />\r\n\r\n                    {/* Navigation dots */}\r\n                    <div className=\"absolute bottom-4 right-4 flex space-x-2 z-30\">\r\n                        {carouselImages.map((_, index) => (\r\n                            <button\r\n                                key={index}\r\n                                onClick={() => setCurrentImageIndex(index)}\r\n                                className={`w-2 h-2 rounded-full transition-all duration-300 ${index === currentImageIndex\r\n                                    ? 'bg-white scale-110'\r\n                                    : 'bg-white/50 hover:bg-white/70'\r\n                                    }`}\r\n                                aria-label={`Go to slide ${index + 1}`}\r\n                            />\r\n                        ))}\r\n                    </div>\r\n                </div>\r\n                <div className=\"absolute w-full h-full bg-gradient-to-b from-black top-0 z-20\"></div>\r\n                <motion.div\r\n                    className=\"flex flex-col z-30 absolute top-0 text-white p-8 sm:p-16 space-y-6 sm:space-y-5\"\r\n                    initial={{ y: 50, opacity: 0 }}\r\n                    whileInView={{ y: 0, opacity: 1 }}\r\n                    transition={{ duration: 0.6, delay: 0.3 }}\r\n                    viewport={{ once: true }}\r\n                >\r\n                    <h1 className=\"text-2xl sm:text-4xl font-medium tracking-wide\">Ugochickwu.</h1>\r\n                    <div className=\"relative\">\r\n                        <div\r\n                            className={`relative overflow-hidden transition-all duration-500 ease-in-out\r\n                                ${!isExpanded ? 'max-h-[85px]' : 'max-h-[500px]'} sm:max-h-none`}\r\n                        >\r\n                            <p className=\"text-sm sm:text-base font-normal leading-[1.8] sm:leading-[2] max-w-[95%] sm:max-w-full\">\r\n                                Founded in 2005 by Ali Ugochicku (aka Fugo), D&apos;Fugo Hair has established itself as a premier\r\n                                destination for luxury wigs in Nigeria and beyond.\r\n                                With over two decades of expertise, we&apos;ve built our reputation on providing premium quality\r\n                                wigs and exceptional service to our clients worldwide.\r\n\r\n                                From our base in Nigeria, we&apos;ve grown into an international brand, bringing sophisticated,\r\n                                high-end hair solutions to discerning customers across the globe. Our commitment to excellence\r\n                                and attention to detail has made us a trusted name in the luxury wig industry.\r\n                            </p>\r\n                        </div>\r\n                        {!isExpanded && (\r\n                            <div className=\"absolute bottom-0 w-full h-16 sm:hidden pointer-events-none\" />\r\n                        )}\r\n                        <button\r\n                            onClick={() => setIsExpanded(!isExpanded)}\r\n                            className=\"sm:hidden mt-2 text-sm font-medium text-white/80 hover:text-white flex items-center gap-1\"\r\n                        >\r\n                            {isExpanded ? 'Show Less' : 'Read More...'}\r\n                        </button>\r\n                    </div>\r\n                </motion.div>\r\n            </motion.div>\r\n            {/* Changed the background div to be hidden on mobile and visible only on sm breakpoint and up */}\r\n            <div className=\"hidden sm:block w-full bg-gradient-to-r from-transparent via-[#ededed] to-transparent z-[-10] absolute h-[85%]\"></div>\r\n        </div>\r\n    );\r\n};\r\n\r\n\r\nexport default AboutFugo;"], "names": [], "mappings": ";;;;AACA;AAEA;AADA;;;AAFA;;;;AAKA,MAAM,YAAY;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,0BAA0B;IAC1B,MAAM,iBAAiB;QACnB;QACA;QACA;QACA;QACA;QACA;KACH;IAED,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACN,MAAM,gBAAgB;qDAAY;oBAC9B;6DAAqB,CAAA,YAAa,CAAC,YAAY,CAAC,IAAI,eAAe,MAAM;;gBAC7E;oDAAG;YAEH;uCAAO,IAAM,cAAc;;QAC/B;8BAAG;QAAC,eAAe,MAAM;KAAC;IAE1B,gMAAgM;IAEhM,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACP,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAI;gBAClC,aAAa;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBACpC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,UAAU;oBAAE,MAAM;gBAAK;;kCAGvB,6LAAC;wBAAI,WAAU;;4BACV,eAAe,GAAG,CAAC,CAAC,OAAO,sBACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAEP,WAAU;oCACV,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCACL,SAAS,UAAU,oBAAoB,IAAI;wCAC3C,OAAO,UAAU,oBAAoB;4CAAC;4CAAG;yCAAK,GAAG;wCACjD,GAAG,UAAU,oBAAoB;4CAAC;4CAAM;yCAAM,GAAG;oCACrD;oCACA,YAAY;wCACR,SAAS;4CAAE,UAAU;wCAAE;wCACvB,OAAO;4CAAE,UAAU;4CAAG,MAAM;wCAAY;wCACxC,GAAG;4CAAE,UAAU;4CAAG,MAAM;wCAAY;oCACxC;8CAEA,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACF,WAAU;wCACV,IAAI;wCACJ,OAAM;wCACN,KAAK;wCACL,KAAK,CAAC,aAAa,EAAE,QAAQ,GAAG;wCAChC,UAAU,UAAU;;;;;;mCApBnB;;;;;0CA0Bb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACP,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCACL,SAAS;wCAAC;wCAAG;wCAAK;qCAAE;gCACxB;gCACA,YAAY;oCACR,UAAU;oCACV,MAAM;oCACN,QAAQ;oCACR,aAAa;gCACjB;;;;;;0CAIJ,6LAAC;gCAAI,WAAU;0CACV,eAAe,GAAG,CAAC,CAAC,GAAG,sBACpB,6LAAC;wCAEG,SAAS,IAAM,qBAAqB;wCACpC,WAAW,CAAC,iDAAiD,EAAE,UAAU,oBACnE,uBACA,iCACA;wCACN,cAAY,CAAC,YAAY,EAAE,QAAQ,GAAG;uCANjC;;;;;;;;;;;;;;;;kCAWrB,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACP,WAAU;wBACV,SAAS;4BAAE,GAAG;4BAAI,SAAS;wBAAE;wBAC7B,aAAa;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;;0CAEvB,6LAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAC/D,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCACG,WAAW,CAAC;gCACR,EAAE,CAAC,aAAa,iBAAiB,gBAAgB,cAAc,CAAC;kDAEpE,cAAA,6LAAC;4CAAE,WAAU;sDAA0F;;;;;;;;;;;oCAW1G,CAAC,4BACE,6LAAC;wCAAI,WAAU;;;;;;kDAEnB,6LAAC;wCACG,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;kDAET,aAAa,cAAc;;;;;;;;;;;;;;;;;;;;;;;;0BAM5C,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAG3B;GAtIM;KAAA;uCAyIS"}}, {"offset": {"line": 3487, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3493, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/ContactForm.tsx"], "sourcesContent": ["\"use client\";\r\nimport { motion } from \"framer-motion\";\r\nimport Image from \"next/image\";\r\nimport React from \"react\";\r\n\r\nconst ContactForm = () => {\r\n    const [message, setMessage] = React.useState(\"\");\r\n\r\n    const handleSendMessage = () => {\r\n        if (message.trim()) {\r\n            const encodedMessage = encodeURIComponent(message);\r\n            window.open(`https://wa.me/2347016027618?text=${encodedMessage}`, '_blank');\r\n            setMessage(\"\"); // Clear the input after sending\r\n        }\r\n    };\r\n\r\n    return (\r\n        // Add new styling\r\n        <div className=\"flex flex-col md:flex-row justify-around relative bg-gradient-to-b from-[#ededed] to-transparent p-4 rounded-[20px] md:rounded-[50px] m-4 md:m-11 md:p-10\">\r\n            {/* WhatsApp Section */}\r\n            <motion.div\r\n                className=\"flex flex-col p-4 md:p-20 space-y-4 md:space-y-14\"\r\n                initial={{ x: -100, opacity: 0 }} // Initial state: slide in from the left and invisible\r\n                whileInView={{ x: 0, opacity: 1 }} // Animate to: slide to position and become visible\r\n                transition={{ duration: 0.6 }} // Animation duration\r\n                viewport={{ once: true }} // Trigger animation only once\r\n            >\r\n                <div className=\"flex flex-col\">\r\n                    <div className=\"font-medium text-xs md:text-base text-black\">Send us a.</div>\r\n                    <div className=\"text-lg md:text-2xl text-black\">Whatsapp Message</div>\r\n                </div>\r\n\r\n                <motion.div\r\n                    className=\"w-full md:w-[550px] h-[80px] md:h-[150px] bg-[#d4d4d4] border-2 border-white rounded-[10px] md:rounded-[20px] relative flex flex-col justify-end p-3 md:p-4\"\r\n                    initial={{ scale: 0.9, opacity: 0 }}\r\n                    whileInView={{ scale: 1, opacity: 1 }}\r\n                    transition={{ duration: 0.6, delay: 0.3 }}\r\n                    viewport={{ once: true }}\r\n                >\r\n                    <div className=\"flex items-center w-full\">\r\n                        <input\r\n                            type=\"text\"\r\n                            value={message}\r\n                            onChange={(e) => setMessage(e.target.value)}\r\n                            className=\"w-full bg-transparent border-none outline-none text-black placeholder-black text-xs md:text-base font-thin\"\r\n                            placeholder=\"Type your message...\"\r\n                            onKeyPress={(e) => {\r\n                                if (e.key === 'Enter') {\r\n                                    handleSendMessage();\r\n                                }\r\n                            }}\r\n                        />\r\n                        <motion.div\r\n                            onClick={handleSendMessage}\r\n                            className=\"ml-2 cursor-pointer\"\r\n                            whileHover={{ scale: 1.2 }}\r\n                            whileTap={{ scale: 0.9 }}\r\n                            transition={{ duration: 0.2 }}\r\n                        >\r\n                            <Image className=\"object-contain w-6 md:w-8\" width={32} height={32} src=\"/icons/share.png\" alt=\"send\" />\r\n                        </motion.div>\r\n                    </div>\r\n                </motion.div>\r\n            </motion.div>\r\n\r\n            {/* Social Media Section */}\r\n            <motion.div\r\n                className=\"flex flex-col p-4 md:p-20 space-y-4 md:space-y-14\"\r\n                initial={{ x: 100, opacity: 0 }}\r\n                whileInView={{ x: 0, opacity: 1 }}\r\n                transition={{ duration: 0.6 }} // Animation duration\r\n                viewport={{ once: true }} // Trigger animation only once\r\n            >\r\n                <div className=\"flex flex-col\">\r\n                    <div className=\"font-medium text-xs md:text-base text-black\">Follow us via.</div>\r\n                    <div className=\"text-lg md:text-2xl text-black\">Our Social Media</div>\r\n                </div>\r\n\r\n                <motion.div\r\n                    className=\"w-full md:w-[350px] h-[180px] md:h-[250px] bg-[#d4d4d4] border-2 border-white rounded-[10px] md:rounded-[20px] p-3 md:p-6 space-y-3 md:space-y-6\"\r\n                    initial={{ scale: 0.9, opacity: 0 }} // Initial state: slightly smaller and invisible\r\n                    whileInView={{ scale: 1, opacity: 1 }} // Animate to: full size and visible\r\n                    transition={{ duration: 0.6, delay: 0.3 }} // Animation duration with delay\r\n                    viewport={{ once: true }} // Trigger animation only once\r\n                >\r\n                    {/* instagram */}\r\n                    <motion.a\r\n                        href=\"https://www.instagram.com/Cepoka Beauty Hub\"\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className=\"flex items-center space-x-2 md:space-x-4 p-2 rounded cursor-pointer\"\r\n                        whileHover={{ scale: 1.05, backgroundColor: \"#d3d3d3\" }}\r\n                        whileTap={{ scale: 0.95 }}\r\n                        transition={{ duration: 0.2 }}\r\n                    >\r\n                        <Image className=\"object-contain w-4 md:w-5\" width={20} height={20} src=\"/icons/instagram.png\" alt=\"instagram\" />\r\n                        <div className=\"font-normal text-xs md:text-base\">@Cepoka Beauty Hub</div>\r\n                    </motion.a>\r\n                    <motion.div className=\"w-full h-[1px] bg-white\" initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.6, delay: 0.3 }}></motion.div>\r\n                    {/* tiktok */}\r\n                    <motion.a\r\n                        href=\"https://www.tiktok.com/@Cepoka Beauty Hub\"\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className=\"flex items-center space-x-2 md:space-x-4 p-2 rounded cursor-pointer\"\r\n                        whileHover={{ scale: 1.05, backgroundColor: \"#d3d3d3\" }}\r\n                        whileTap={{ scale: 0.95 }}\r\n                        transition={{ duration: 0.2 }}\r\n                    >\r\n                        <Image className=\"object-contain w-4 md:w-5\" width={20} height={20} src=\"/icons/tiktok.png\" alt=\"tiktok\" />\r\n                        <div className=\"font-normal text-xs md:text-base\">@Cepoka Beauty Hub</div>\r\n                    </motion.a>\r\n                    <motion.div className=\"w-full h-[1px] bg-white\" initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.6, delay: 0.3 }}></motion.div>\r\n                    {/* whatsapp */}\r\n                    <motion.a\r\n                        href=\"https://wa.me/2348033262453?text=Hello!%20I'm%20interested%20in%20your%20wigs.%20\"\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className=\"flex items-center space-x-2 md:space-x-4 p-2 rounded cursor-pointer\"\r\n                        whileHover={{ scale: 1.05, backgroundColor: \"#d3d3d3\" }}\r\n                        whileTap={{ scale: 0.95 }}\r\n                        transition={{ duration: 0.2 }}\r\n                    >\r\n                        <Image className=\"object-contain w-4 md:w-5\" width={20} height={20} src=\"/icons/whatsapp.png\" alt=\"whatsapp\" />\r\n                        <div className=\"font-normal text-xs md:text-base\">@Cepoka Beauty Hub</div>\r\n                    </motion.a>\r\n                </motion.div>\r\n            </motion.div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default ContactForm;"], "names": [], "mappings": ";;;;AAEA;AACA;AAFA;;;AADA;;;;AAKA,MAAM,cAAc;;IAChB,MAAM,CAAC,SAAS,WAAW,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAE7C,MAAM,oBAAoB;QACtB,IAAI,QAAQ,IAAI,IAAI;YAChB,MAAM,iBAAiB,mBAAmB;YAC1C,OAAO,IAAI,CAAC,CAAC,iCAAiC,EAAE,gBAAgB,EAAE;YAClE,WAAW,KAAK,gCAAgC;QACpD;IACJ;IAEA,OACI,kBAAkB;kBAClB,6LAAC;QAAI,WAAU;;0BAEX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACP,WAAU;gBACV,SAAS;oBAAE,GAAG,CAAC;oBAAK,SAAS;gBAAE;gBAC/B,aAAa;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,UAAU;oBAAE,MAAM;gBAAK;;kCAEvB,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;0CAA8C;;;;;;0CAC7D,6LAAC;gCAAI,WAAU;0CAAiC;;;;;;;;;;;;kCAGpD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACP,WAAU;wBACV,SAAS;4BAAE,OAAO;4BAAK,SAAS;wBAAE;wBAClC,aAAa;4BAAE,OAAO;4BAAG,SAAS;wBAAE;wBACpC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;kCAEvB,cAAA,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCACG,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC1C,WAAU;oCACV,aAAY;oCACZ,YAAY,CAAC;wCACT,IAAI,EAAE,GAAG,KAAK,SAAS;4CACnB;wCACJ;oCACJ;;;;;;8CAEJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACP,SAAS;oCACT,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAI;oCACvB,YAAY;wCAAE,UAAU;oCAAI;8CAE5B,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCAAC,WAAU;wCAA4B,OAAO;wCAAI,QAAQ;wCAAI,KAAI;wCAAmB,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO/G,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACP,WAAU;gBACV,SAAS;oBAAE,GAAG;oBAAK,SAAS;gBAAE;gBAC9B,aAAa;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,UAAU;oBAAE,MAAM;gBAAK;;kCAEvB,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;0CAA8C;;;;;;0CAC7D,6LAAC;gCAAI,WAAU;0CAAiC;;;;;;;;;;;;kCAGpD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACP,WAAU;wBACV,SAAS;4BAAE,OAAO;4BAAK,SAAS;wBAAE;wBAClC,aAAa;4BAAE,OAAO;4BAAG,SAAS;wBAAE;wBACpC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;;0CAGvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACL,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,WAAU;gCACV,YAAY;oCAAE,OAAO;oCAAM,iBAAiB;gCAAU;gCACtD,UAAU;oCAAE,OAAO;gCAAK;gCACxB,YAAY;oCAAE,UAAU;gCAAI;;kDAE5B,6LAAC,gIAAA,CAAA,UAAK;wCAAC,WAAU;wCAA4B,OAAO;wCAAI,QAAQ;wCAAI,KAAI;wCAAuB,KAAI;;;;;;kDACnG,6LAAC;wCAAI,WAAU;kDAAmC;;;;;;;;;;;;0CAEtD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,WAAU;gCAA0B,SAAS;oCAAE,SAAS;gCAAE;gCAAG,SAAS;oCAAE,SAAS;gCAAE;gCAAG,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;;;;;;0CAE1I,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACL,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,WAAU;gCACV,YAAY;oCAAE,OAAO;oCAAM,iBAAiB;gCAAU;gCACtD,UAAU;oCAAE,OAAO;gCAAK;gCACxB,YAAY;oCAAE,UAAU;gCAAI;;kDAE5B,6LAAC,gIAAA,CAAA,UAAK;wCAAC,WAAU;wCAA4B,OAAO;wCAAI,QAAQ;wCAAI,KAAI;wCAAoB,KAAI;;;;;;kDAChG,6LAAC;wCAAI,WAAU;kDAAmC;;;;;;;;;;;;0CAEtD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,WAAU;gCAA0B,SAAS;oCAAE,SAAS;gCAAE;gCAAG,SAAS;oCAAE,SAAS;gCAAE;gCAAG,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;;;;;;0CAE1I,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACL,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,WAAU;gCACV,YAAY;oCAAE,OAAO;oCAAM,iBAAiB;gCAAU;gCACtD,UAAU;oCAAE,OAAO;gCAAK;gCACxB,YAAY;oCAAE,UAAU;gCAAI;;kDAE5B,6LAAC,gIAAA,CAAA,UAAK;wCAAC,WAAU;wCAA4B,OAAO;wCAAI,QAAQ;wCAAI,KAAI;wCAAsB,KAAI;;;;;;kDAClG,6LAAC;wCAAI,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1E;GA7HM;KAAA;uCA+HS"}}, {"offset": {"line": 3887, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3893, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/AdminAccessModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface AdminAccessModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onAccess: (key: string) => void;\n}\n\nexport default function AdminAccessModal({ isOpen, onClose, onAccess }: AdminAccessModalProps) {\n  const [adminKey, setAdminKey] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  // Reset admin key when modal is closed\n  useEffect(() => {\n    if (!isOpen) {\n      setAdminKey('');\n    }\n  }, [isOpen]);\n\n  // Prevent background scrolling when modal is open\n  useEffect(() => {\n    if (typeof window !== 'undefined' && isOpen) {\n      // Store the current scroll position\n      const scrollPos = window.scrollY;\n\n      // Prevent background scrolling in a less intrusive way\n      const originalStyle = window.getComputedStyle(document.body).overflow;\n      document.body.style.overflow = 'hidden';\n\n      // Add a high z-index to the modal container\n      const modalContainer = document.getElementById('admin-modal-container');\n      if (modalContainer) {\n        modalContainer.style.zIndex = '9999';\n      }\n\n      return () => {\n        // Restore background scrolling\n        document.body.style.overflow = originalStyle || '';\n\n        // Restore scroll position to prevent jumping\n        setTimeout(() => {\n          window.scrollTo(0, scrollPos);\n        }, 0);\n      };\n    }\n\n    return undefined;\n  }, [isOpen]);\n\n  const handleSubmit = useCallback(() => {\n    // Pass the admin key to the parent component\n    if (adminKey.trim() !== '') {\n      setIsLoading(true);\n\n      // Directly check if the key is correct\n      if (adminKey === 'fugo101') {\n        console.log(\"Correct admin key entered in modal:\", adminKey);\n      }\n\n      // Simulate a slight delay for authentication\n      setTimeout(() => {\n        onAccess(adminKey);\n        // Note: We don't set isLoading to false here because the modal will be closed\n        // or the parent component will handle the error state\n      }, 800);\n    }\n  }, [adminKey, onAccess]);\n\n  // Function to cancel verification process\n  const handleCancel = useCallback(() => {\n    if (isLoading) {\n      setIsLoading(false);\n      console.log(\"Verification process cancelled\");\n    } else {\n      onClose();\n    }\n  }, [isLoading, onClose]);\n\n  // Focus the input field when the modal opens\n  useEffect(() => {\n    if (isOpen && inputRef.current) {\n      // Short delay to ensure the modal is fully rendered\n      setTimeout(() => {\n        inputRef.current?.focus();\n      }, 100);\n    }\n  }, [isOpen]);\n\n  // Handle Enter key press\n  useEffect(() => {\n    const handleKeyPress = (event: KeyboardEvent) => {\n      if (event.key === 'Enter') {\n        handleSubmit();\n      }\n    };\n\n    if (isOpen && typeof window !== 'undefined') {\n      window.addEventListener('keydown', handleKeyPress);\n      return () => window.removeEventListener('keydown', handleKeyPress);\n    }\n  }, [isOpen, handleSubmit]);\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <motion.div\n          id=\"admin-modal-container\"\n          className=\"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-[9999] p-4\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          onClick={(e) => {\n            e.preventDefault();\n            e.stopPropagation();\n            onClose();\n          }}\n          style={{\n            height: '100%',\n            width: '100%',\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            touchAction: 'manipulation', // Better touch handling\n            userSelect: 'none', // Prevent text selection\n            pointerEvents: 'auto' // Ensure pointer events are enabled\n          }}\n        >\n          <motion.div\n            className=\"bg-white p-4 sm:p-6 rounded-lg shadow-xl w-full max-w-[320px] sm:max-w-md relative\"\n            initial={{ scale: 0.8, y: 0 }}\n            animate={{ scale: 1, y: 0 }}\n            exit={{ scale: 0.8, y: 0 }}\n            style={{\n              position: 'relative',\n              pointerEvents: 'auto',\n              touchAction: 'manipulation',\n              zIndex: 10000\n            }}\n            onClick={(e) => {\n              e.preventDefault();\n              e.stopPropagation();\n            }}\n          >\n            <h3 className=\"text-gray-800 font-bold text-lg mb-3 sm:mb-4\">Admin Access</h3>\n\n            <input\n              ref={inputRef}\n              type=\"password\"\n              value={adminKey}\n              onChange={(e) => setAdminKey(e.target.value)}\n              placeholder=\"Enter admin key\"\n              className={`w-full px-3 sm:px-4 py-2 sm:py-2.5 border rounded-lg mb-3 sm:mb-4 font-[500] text-black ${isLoading ? 'opacity-70 cursor-not-allowed' : ''\n                }`}\n              autoComplete=\"off\"\n              autoCorrect=\"off\"\n              autoCapitalize=\"off\"\n              spellCheck=\"false\"\n              disabled={isLoading}\n              style={{ touchAction: 'manipulation' }}\n            />\n\n            <div className=\"flex justify-end space-x-2 sm:space-x-3\">\n              <button\n                onClick={(e) => {\n                  e.preventDefault();\n                  e.stopPropagation();\n                  handleCancel();\n                }}\n                className={`px-3 sm:px-4 py-2 font-[500] ${isLoading\n                  ? 'bg-red-100 text-red-600 hover:bg-red-200'\n                  : 'text-gray-600 hover:text-gray-800'}\n                  transition-colors duration-200 rounded-lg`}\n                style={{ touchAction: 'manipulation' }}\n              >\n                {isLoading ? 'Stop Verification' : 'Cancel'}\n              </button>\n              <button\n                onClick={(e) => {\n                  e.preventDefault();\n                  e.stopPropagation();\n                  handleSubmit();\n                }}\n                disabled={isLoading}\n                className={`px-3 sm:px-4 py-2 font-[500] bg-[#333333] text-white rounded-lg hover:bg-gray-800 transition-colors duration-200 ${isLoading ? 'opacity-90' : ''\n                  }`}\n                style={{ touchAction: 'manipulation' }}\n              >\n                {isLoading ? (\n                  <span className=\"flex items-center gap-2\">\n                    <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                      <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                      <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                    </svg>\n                    Verifying...\n                  </span>\n                ) : (\n                  'Access'\n                )}\n              </button>\n            </div>\n          </motion.div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAWe,SAAS,iBAAiB,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAyB;;IAC3F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,QAAQ;gBACX,YAAY;YACd;QACF;qCAAG;QAAC;KAAO;IAEX,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,aAAkB,eAAe,QAAQ;gBAC3C,oCAAoC;gBACpC,MAAM,YAAY,OAAO,OAAO;gBAEhC,uDAAuD;gBACvD,MAAM,gBAAgB,OAAO,gBAAgB,CAAC,SAAS,IAAI,EAAE,QAAQ;gBACrE,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBAE/B,4CAA4C;gBAC5C,MAAM,iBAAiB,SAAS,cAAc,CAAC;gBAC/C,IAAI,gBAAgB;oBAClB,eAAe,KAAK,CAAC,MAAM,GAAG;gBAChC;gBAEA;kDAAO;wBACL,+BAA+B;wBAC/B,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,iBAAiB;wBAEhD,6CAA6C;wBAC7C;0DAAW;gCACT,OAAO,QAAQ,CAAC,GAAG;4BACrB;yDAAG;oBACL;;YACF;YAEA,OAAO;QACT;qCAAG;QAAC;KAAO;IAEX,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAC/B,6CAA6C;YAC7C,IAAI,SAAS,IAAI,OAAO,IAAI;gBAC1B,aAAa;gBAEb,uCAAuC;gBACvC,IAAI,aAAa,WAAW;oBAC1B,QAAQ,GAAG,CAAC,uCAAuC;gBACrD;gBAEA,6CAA6C;gBAC7C;kEAAW;wBACT,SAAS;oBACT,8EAA8E;oBAC9E,sDAAsD;oBACxD;iEAAG;YACL;QACF;qDAAG;QAAC;QAAU;KAAS;IAEvB,0CAA0C;IAC1C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAC/B,IAAI,WAAW;gBACb,aAAa;gBACb,QAAQ,GAAG,CAAC;YACd,OAAO;gBACL;YACF;QACF;qDAAG;QAAC;QAAW;KAAQ;IAEvB,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,UAAU,SAAS,OAAO,EAAE;gBAC9B,oDAAoD;gBACpD;kDAAW;wBACT,SAAS,OAAO,EAAE;oBACpB;iDAAG;YACL;QACF;qCAAG;QAAC;KAAO;IAEX,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;6DAAiB,CAAC;oBACtB,IAAI,MAAM,GAAG,KAAK,SAAS;wBACzB;oBACF;gBACF;;YAEA,IAAI,UAAU,aAAkB,aAAa;gBAC3C,OAAO,gBAAgB,CAAC,WAAW;gBACnC;kDAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;YACrD;QACF;qCAAG;QAAC;QAAQ;KAAa;IAEzB,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,IAAG;YACH,WAAU;YACV,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,SAAS,CAAC;gBACR,EAAE,cAAc;gBAChB,EAAE,eAAe;gBACjB;YACF;YACA,OAAO;gBACL,QAAQ;gBACR,OAAO;gBACP,UAAU;gBACV,KAAK;gBACL,MAAM;gBACN,aAAa;gBACb,YAAY;gBACZ,eAAe,OAAO,oCAAoC;YAC5D;sBAEA,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,OAAO;oBAAK,GAAG;gBAAE;gBAC5B,SAAS;oBAAE,OAAO;oBAAG,GAAG;gBAAE;gBAC1B,MAAM;oBAAE,OAAO;oBAAK,GAAG;gBAAE;gBACzB,OAAO;oBACL,UAAU;oBACV,eAAe;oBACf,aAAa;oBACb,QAAQ;gBACV;gBACA,SAAS,CAAC;oBACR,EAAE,cAAc;oBAChB,EAAE,eAAe;gBACnB;;kCAEA,6LAAC;wBAAG,WAAU;kCAA+C;;;;;;kCAE7D,6LAAC;wBACC,KAAK;wBACL,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wBAC3C,aAAY;wBACZ,WAAW,CAAC,wFAAwF,EAAE,YAAY,kCAAkC,IAChJ;wBACJ,cAAa;wBACb,aAAY;wBACZ,gBAAe;wBACf,YAAW;wBACX,UAAU;wBACV,OAAO;4BAAE,aAAa;wBAAe;;;;;;kCAGvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,CAAC;oCACR,EAAE,cAAc;oCAChB,EAAE,eAAe;oCACjB;gCACF;gCACA,WAAW,CAAC,6BAA6B,EAAE,YACvC,6CACA,oCAAoC;2DACG,CAAC;gCAC5C,OAAO;oCAAE,aAAa;gCAAe;0CAEpC,YAAY,sBAAsB;;;;;;0CAErC,6LAAC;gCACC,SAAS,CAAC;oCACR,EAAE,cAAc;oCAChB,EAAE,eAAe;oCACjB;gCACF;gCACA,UAAU;gCACV,WAAW,CAAC,iHAAiH,EAAE,YAAY,eAAe,IACtJ;gCACJ,OAAO;oCAAE,aAAa;gCAAe;0CAEpC,0BACC,6LAAC;oCAAK,WAAU;;sDACd,6LAAC;4CAAI,WAAU;4CAA6C,OAAM;4CAA6B,MAAK;4CAAO,SAAQ;;8DACjH,6LAAC;oDAAO,WAAU;oDAAa,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAK,QAAO;oDAAe,aAAY;;;;;;8DACxF,6LAAC;oDAAK,WAAU;oDAAa,MAAK;oDAAe,GAAE;;;;;;;;;;;;wCAC/C;;;;;;2CAIR;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB;GAtMwB;KAAA"}}, {"offset": {"line": 4215, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4221, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/LoadingScreen.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport SpinningLoader from './SpinningLoader';\n\ninterface LoadingScreenProps {\n  message?: string;\n  isFullScreen?: boolean;\n}\n\nexport default function LoadingScreen({ \n  message = 'Loading...', \n  isFullScreen = true \n}: LoadingScreenProps) {\n  return (\n    <motion.div\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      exit={{ opacity: 0 }}\n      className={`flex flex-col items-center justify-center bg-white bg-opacity-90 z-[9999] ${\n        isFullScreen ? 'fixed inset-0' : 'absolute inset-0'\n      }`}\n    >\n      <div className=\"text-center p-6 rounded-lg\">\n        <SpinningLoader size=\"large\" />\n        <p className=\"mt-4 text-gray-700 font-medium text-lg\">{message}</p>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AADA;AAFA;;;;AAUe,SAAS,cAAc,EACpC,UAAU,YAAY,EACtB,eAAe,IAAI,EACA;IACnB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,MAAM;YAAE,SAAS;QAAE;QACnB,WAAW,CAAC,0EAA0E,EACpF,eAAe,kBAAkB,oBACjC;kBAEF,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,8IAAA,CAAA,UAAc;oBAAC,MAAK;;;;;;8BACrB,6LAAC;oBAAE,WAAU;8BAA0C;;;;;;;;;;;;;;;;;AAI/D;KAnBwB"}}, {"offset": {"line": 4279, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4285, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/Footer.tsx"], "sourcesContent": ["\"use client\"\r\nimport Image from 'next/image'\r\nimport Link from 'next/link'\r\nimport { useEffect, useState } from \"react\";\r\nimport AdminAccessModal from './AdminAccessModal';\r\nimport toast from 'react-hot-toast';\r\nimport LoadingScreen from './LoadingScreen';\r\nimport * as ReactDOM from 'react-dom/client';\r\n\r\nconst Footer = () => {\r\n    const [isMounted, setIsMounted] = useState(false);\r\n    const [showAdminPrompt, setShowAdminPrompt] = useState(false);\r\n    const [adminKey, setAdminKey] = useState('');\r\n\r\n    useEffect(() => {\r\n        setIsMounted(true);\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        // Define handleAdminAccess inside the useEffect to avoid dependency issues\r\n        const handleAdminAccess = () => {\r\n            if (!isMounted || typeof window === 'undefined') return;\r\n\r\n            try {\r\n                // Get the current value directly from state\r\n                const currentKey = adminKey;\r\n                console.log(\"Checking admin key:\", currentKey);\r\n\r\n                // Hardcoded comparison with the exact string\r\n                if (currentKey === \"fugo101\") {\r\n                    console.log(\"Admin key is valid, setting localStorage and redirecting\");\r\n                    // Store the exact string\r\n                    localStorage.setItem('adminKey', \"fugo101\");\r\n                    // Redirect to admin page\r\n                    window.location.href = '/admin';\r\n                } else {\r\n                    console.log(\"Invalid admin key:\", currentKey);\r\n                    alert('Invalid admin key');\r\n                    setAdminKey('');\r\n                }\r\n            } catch (error) {\r\n                console.error('Error accessing admin:', error);\r\n                alert('An error occurred');\r\n            }\r\n        };\r\n\r\n        const handleKeyPress = (event: KeyboardEvent) => {\r\n            if (event.key === 'Enter') {\r\n                handleAdminAccess();\r\n            }\r\n        };\r\n\r\n        if (showAdminPrompt && typeof window !== 'undefined') {\r\n            window.addEventListener('keydown', handleKeyPress);\r\n            return () => window.removeEventListener('keydown', handleKeyPress);\r\n        }\r\n    }, [showAdminPrompt, isMounted, adminKey]);\r\n\r\n    const handleGetDirections = () => {\r\n        if (!isMounted || typeof window === 'undefined') return;\r\n        try {\r\n            window?.open(`https://www.google.com/maps/search/?api=1&query=6.456559134970387,3.3842979366622847`);\r\n        } catch (error) {\r\n            console.error('Error getting directions:', error);\r\n            alert('An error occurred');\r\n        }\r\n    };\r\n\r\n    const handleCloseModal = () => {\r\n        setShowAdminPrompt(false);\r\n        setAdminKey('');\r\n    };\r\n\r\n    return (\r\n        <footer className=\"relative w-full bg-[#ededed] text-black pt-24 pb-6 font-light mt-auto\">\r\n            {/* Top fade gradient */}\r\n            <div className=\"absolute top-0 left-0 w-full h-24 bg-gradient-to-b from-white via-[#ededed]/50 to-[#ededed] z-10\" />\r\n\r\n            <AdminAccessModal\r\n                isOpen={showAdminPrompt}\r\n                onClose={handleCloseModal}\r\n                onAccess={(key) => {\r\n                    console.log(\"Received admin key:\", key);\r\n\r\n                    // Direct check without using state\r\n                    if (key === \"fugo101\") {\r\n                        console.log(\"Valid admin key, redirecting...\");\r\n                        localStorage.setItem('adminKey', \"fugo101\");\r\n\r\n                        // Add the LoadingScreen component to the DOM\r\n                        const loadingScreenContainer = document.createElement('div');\r\n                        loadingScreenContainer.id = 'admin-loading-screen';\r\n                        document.body.appendChild(loadingScreenContainer);\r\n\r\n                        // Render the LoadingScreen component\r\n                        const root = ReactDOM.createRoot(loadingScreenContainer);\r\n                        root.render(\r\n                            <LoadingScreen message=\"Accessing Admin Panel...\" isFullScreen={true} />\r\n                        );\r\n\r\n                        // Add a slight delay before redirecting to show the loading state\r\n                        setTimeout(() => {\r\n                            // Clean up the loading screen container before navigation\r\n                            const loadingScreenContainer = document.getElementById('admin-loading-screen');\r\n                            if (loadingScreenContainer) {\r\n                                document.body.removeChild(loadingScreenContainer);\r\n                            }\r\n                            window.location.href = '/admin';\r\n                        }, 800);\r\n                    } else {\r\n                        console.log(\"Invalid admin key:\", key);\r\n                        // Add a slight delay before showing the alert to make the loading state visible\r\n                        setTimeout(() => {\r\n                            toast.error('Invalid admin key', {\r\n                                duration: 3000,\r\n                                position: 'top-center',\r\n                                style: {\r\n                                    background: '#FEE2E2',\r\n                                    color: '#B91C1C',\r\n                                    fontWeight: 'bold'\r\n                                },\r\n                            });\r\n                        }, 500);\r\n                    }\r\n                }}\r\n            />\r\n            <div className=\"container mx-auto px-4\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\r\n                    {/* Logo and Description */}\r\n                    <div className=\"flex flex-col space-y-4\">\r\n                        <Image\r\n                            src=\"/logo.png\"\r\n                            alt=\"DFugo Hair\"\r\n                            width={50}\r\n                            height={60}\r\n                            className=\"mb-4\"\r\n                        />\r\n                        <p className=\"text-black text-sm font-light\">\r\n                            Premium quality hair products and accessories for all your styling needs.\r\n                        </p>\r\n                    </div>\r\n\r\n                    {/* Quick Links */}\r\n                    <div>\r\n                        <h3 className=\"text-lg font-medium mb-4\">Quick Links</h3>\r\n                        <ul className=\"space-y-2\">\r\n                            <li><Link href=\"/\" className=\"text-black hover:text-black transition font-light\">Home</Link></li>\r\n                            <li><Link href=\"/shop\" className=\"text-black hover:text-black transition font-light\">Shop</Link></li>\r\n                            <li><Link href=\"/about\" className=\"text-black hover:text-black transition font-light\">About Us</Link></li>\r\n                            <li><Link href=\"/contact\" className=\"text-black hover:text-black transition font-light\">Contact</Link></li>\r\n                            <li>\r\n                                <button\r\n                                    onClick={() => setShowAdminPrompt(true)}\r\n                                    className=\"text-black hover:text-white transition font-light text-left w-full\"\r\n                                >\r\n                                    Admin\r\n                                </button>\r\n                            </li>\r\n                        </ul>\r\n                    </div>\r\n\r\n                    {/* Contact Info */}\r\n                    <div>\r\n                        <h3 className=\"text-lg font-medium mb-4\">Contact Us</h3>\r\n                        <ul onClick={handleGetDirections} className=\"space-y-2 text-black font-light cursor-pointer\">\r\n                            10, Balogun street<br />\r\n                            Lagos Island, Lagos Nigeria\r\n                        </ul>\r\n                    </div>\r\n\r\n                    {/* Social Media - temporary version without icons */}\r\n                    <div>\r\n                        <h3 className=\"text-lg font-medium mb-4\">Follow Us</h3>\r\n                        <div className=\"flex space-x-4\">\r\n                            <a href=\"https://www.facebook.com\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-black hover:text-white transition\">\r\n                                FB\r\n                            </a>\r\n                            <a href=\"https://www.tiktok.com/@d_fugo_hair\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-black hover:text-white transition\">\r\n                                TK\r\n                            </a>\r\n                            <a href=\"https://www.instagram.com/d_fugo_hair\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-black hover:text-white transition\">\r\n                                IG\r\n                            </a>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Copyright */}\r\n                <div className=\"border-t border-black-300 mt-8 pt-6 text-center text-gray-400 text-sm font-light\">\r\n                    <p>&copy; {new Date().getFullYear()} DFugo Hair. All rights reserved.</p>\r\n                </div>\r\n            </div>\r\n        </footer>\r\n    )\r\n}\r\n\r\nexport default Footer\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;;AASA,MAAM,SAAS;;IACX,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,aAAa;QACjB;2BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,2EAA2E;YAC3E,MAAM;sDAAoB;oBACtB,IAAI,CAAC,aAAa,aAAkB,aAAa;oBAEjD,IAAI;wBACA,4CAA4C;wBAC5C,MAAM,aAAa;wBACnB,QAAQ,GAAG,CAAC,uBAAuB;wBAEnC,6CAA6C;wBAC7C,IAAI,eAAe,WAAW;4BAC1B,QAAQ,GAAG,CAAC;4BACZ,yBAAyB;4BACzB,aAAa,OAAO,CAAC,YAAY;4BACjC,yBAAyB;4BACzB,OAAO,QAAQ,CAAC,IAAI,GAAG;wBAC3B,OAAO;4BACH,QAAQ,GAAG,CAAC,sBAAsB;4BAClC,MAAM;4BACN,YAAY;wBAChB;oBACJ,EAAE,OAAO,OAAO;wBACZ,QAAQ,KAAK,CAAC,0BAA0B;wBACxC,MAAM;oBACV;gBACJ;;YAEA,MAAM;mDAAiB,CAAC;oBACpB,IAAI,MAAM,GAAG,KAAK,SAAS;wBACvB;oBACJ;gBACJ;;YAEA,IAAI,mBAAmB,aAAkB,aAAa;gBAClD,OAAO,gBAAgB,CAAC,WAAW;gBACnC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;YACvD;QACJ;2BAAG;QAAC;QAAiB;QAAW;KAAS;IAEzC,MAAM,sBAAsB;QACxB,IAAI,CAAC,aAAa,aAAkB,aAAa;QACjD,IAAI;YACA,QAAQ,KAAK,CAAC,oFAAoF,CAAC;QACvG,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACV;IACJ;IAEA,MAAM,mBAAmB;QACrB,mBAAmB;QACnB,YAAY;IAChB;IAEA,qBACI,6LAAC;QAAO,WAAU;;0BAEd,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC,gJAAA,CAAA,UAAgB;gBACb,QAAQ;gBACR,SAAS;gBACT,UAAU,CAAC;oBACP,QAAQ,GAAG,CAAC,uBAAuB;oBAEnC,mCAAmC;oBACnC,IAAI,QAAQ,WAAW;wBACnB,QAAQ,GAAG,CAAC;wBACZ,aAAa,OAAO,CAAC,YAAY;wBAEjC,6CAA6C;wBAC7C,MAAM,yBAAyB,SAAS,aAAa,CAAC;wBACtD,uBAAuB,EAAE,GAAG;wBAC5B,SAAS,IAAI,CAAC,WAAW,CAAC;wBAE1B,qCAAqC;wBACrC,MAAM,OAAO,sKAAS,UAAU,CAAC;wBACjC,KAAK,MAAM,eACP,6LAAC,6IAAA,CAAA,UAAa;4BAAC,SAAQ;4BAA2B,cAAc;;;;;;wBAGpE,kEAAkE;wBAClE,WAAW;4BACP,0DAA0D;4BAC1D,MAAM,yBAAyB,SAAS,cAAc,CAAC;4BACvD,IAAI,wBAAwB;gCACxB,SAAS,IAAI,CAAC,WAAW,CAAC;4BAC9B;4BACA,OAAO,QAAQ,CAAC,IAAI,GAAG;wBAC3B,GAAG;oBACP,OAAO;wBACH,QAAQ,GAAG,CAAC,sBAAsB;wBAClC,gFAAgF;wBAChF,WAAW;4BACP,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,qBAAqB;gCAC7B,UAAU;gCACV,UAAU;gCACV,OAAO;oCACH,YAAY;oCACZ,OAAO;oCACP,YAAY;gCAChB;4BACJ;wBACJ,GAAG;oBACP;gBACJ;;;;;;0BAEJ,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;;0CAEX,6LAAC;gCAAI,WAAU;;kDACX,6LAAC,gIAAA,CAAA,UAAK;wCACF,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;kDAEd,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;0CAMjD,6LAAC;;kDACG,6LAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,6LAAC;wCAAG,WAAU;;0DACV,6LAAC;0DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAI,WAAU;8DAAoD;;;;;;;;;;;0DACjF,6LAAC;0DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;8DAAoD;;;;;;;;;;;0DACrF,6LAAC;0DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;8DAAoD;;;;;;;;;;;0DACtF,6LAAC;0DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;8DAAoD;;;;;;;;;;;0DACxF,6LAAC;0DACG,cAAA,6LAAC;oDACG,SAAS,IAAM,mBAAmB;oDAClC,WAAU;8DACb;;;;;;;;;;;;;;;;;;;;;;;0CAQb,6LAAC;;kDACG,6LAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,6LAAC;wCAAG,SAAS;wCAAqB,WAAU;;4CAAiD;0DACvE,6LAAC;;;;;4CAAK;;;;;;;;;;;;;0CAMhC,6LAAC;;kDACG,6LAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAE,MAAK;gDAA2B,QAAO;gDAAS,KAAI;gDAAsB,WAAU;0DAAyC;;;;;;0DAGhI,6LAAC;gDAAE,MAAK;gDAAsC,QAAO;gDAAS,KAAI;gDAAsB,WAAU;0DAAyC;;;;;;0DAG3I,6LAAC;gDAAE,MAAK;gDAAwC,QAAO;gDAAS,KAAI;gDAAsB,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;;kCAQzJ,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;;gCAAE;gCAAQ,IAAI,OAAO,WAAW;gCAAG;;;;;;;;;;;;;;;;;;;;;;;;AAKxD;GAzLM;KAAA;uCA2LS"}}, {"offset": {"line": 4709, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4715, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/WhatsAppButton.tsx"], "sourcesContent": ["\"use client\"\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { useState, useEffect, useRef } from \"react\";\r\nimport Image from \"next/image\";\r\n\r\nconst WhatsAppButton = () => {\r\n    const [isModalOpen, setIsModalOpen] = useState(false);\r\n    const [message, setMessage] = useState(\"\");\r\n    const [isMounted, setIsMounted] = useState(false);\r\n    const modalRef = useRef<HTMLDivElement>(null);\r\n\r\n    useEffect(() => {\r\n        setIsMounted(true);\r\n    }, []);\r\n\r\n    const openWhatsApp = () => {\r\n        if (!isMounted || typeof window === 'undefined') return;\r\n\r\n        if (message.trim() !== \"\") {\r\n            const encodedMessage = encodeURIComponent(message);\r\n            const whatsappURL = `https://wa.me/2347016027618?text=${encodedMessage}`;\r\n            window?.open(whatsappURL, \"_blank\");\r\n            setIsModalOpen(false); // Close modal after sending\r\n            setMessage(\"\"); // Reset message\r\n        } else {\r\n            alert(\"Please enter a message.\");\r\n        }\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <motion.div\r\n                whileHover={{ scale: 1.1 }}\r\n                whileTap={{ scale: 0.9 }}\r\n                onClick={() => setIsModalOpen(true)}\r\n                className=\"bg-gradient-to-tr from-[#1E90FF] to-[#FF69B4]  p-2 w-[60px] h-[60px] \r\n                    md:w-[70px] md:h-[70px] rounded-full fixed bottom-[20px] right-[20px] \r\n                    flex items-center justify-center shadow-lg cursor-pointer z-[999]\r\n                    hover:shadow-xl transition-shadow duration-300\"\r\n                style={{\r\n                    willChange: 'transform',\r\n                    transform: 'translate3d(0, 0, 0)',\r\n                }}\r\n            >\r\n                <Image \r\n                    width={25} \r\n                    height={25} \r\n                    className=\"w-[60%] h-[60%] object-contain\" \r\n                    src={\"/icons/message.png\"} \r\n                    alt={\"WhatsApp message\"} \r\n                />\r\n            </motion.div>\r\n\r\n            <AnimatePresence>\r\n                {isModalOpen && (\r\n                    <motion.div\r\n                        className=\"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center \r\n                            justify-center z-[1000] p-4\"\r\n                        initial={{ opacity: 0 }}\r\n                        animate={{ opacity: 1 }}\r\n                        exit={{ opacity: 0 }}\r\n                        onClick={() => setIsModalOpen(false)}\r\n                    >\r\n                        <motion.div\r\n                            ref={modalRef}\r\n                            className=\"bg-white rounded-lg w-[90%] max-w-md p-4 sm:p-6 \r\n                                shadow-lg relative\"\r\n                            variants={{\r\n                                hidden: { opacity: 0, scale: 0.8, y: 10 },\r\n                                visible: { \r\n                                    opacity: 1, \r\n                                    scale: 1, \r\n                                    y: 0,\r\n                                    transition: {\r\n                                        type: 'spring',\r\n                                        stiffness: 300,\r\n                                        damping: 25\r\n                                    }\r\n                                },\r\n                                exit: {\r\n                                    opacity: 0,\r\n                                    scale: 0.8,\r\n                                    y: 10,\r\n                                    transition: { duration: 0.2 }\r\n                                }\r\n                            }}\r\n                            initial=\"hidden\"\r\n                            animate=\"visible\"\r\n                            exit=\"exit\"\r\n                            onClick={(e) => e.stopPropagation()}\r\n                        >\r\n                            <button\r\n                                onClick={() => setIsModalOpen(false)}\r\n                                className=\"absolute top-2 right-2 text-gray-600 hover:text-black \r\n                                    p-2 rounded-full hover:bg-gray-100 transition-colors\"\r\n                            >\r\n                                ✕\r\n                            </button>\r\n                            <div className=\"mt-4\">\r\n                                <textarea\r\n                                    value={message}\r\n                                    onChange={(e) => setMessage(e.target.value)}\r\n                                    placeholder=\"Type your message here...\"\r\n                                    className=\"w-full h-32 p-3 border border-gray-300 rounded-lg \r\n                                        focus:ring-2 focus:ring-yellow-500 focus:border-transparent\r\n                                        resize-none\"\r\n                                />\r\n                                <button\r\n                                    onClick={openWhatsApp}\r\n                                    className=\"w-full mt-4 bg-gradient-to-r from-[#fe8ef3] to-[#4782ff]\r\n                                        text-white font-medium py-2 px-4 rounded-lg\r\n                                        hover:opacity-90 transition-opacity\"\r\n                                >\r\n                                    Send Message\r\n                                </button>\r\n                            </div>\r\n                        </motion.div>\r\n                    </motion.div>\r\n                )}\r\n            </AnimatePresence>\r\n        </>\r\n    );\r\n};\r\n\r\nexport default WhatsAppButton;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAFA;AAAA;;;AADA;;;;AAKA,MAAM,iBAAiB;;IACnB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAExC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACN,aAAa;QACjB;mCAAG,EAAE;IAEL,MAAM,eAAe;QACjB,IAAI,CAAC,aAAa,aAAkB,aAAa;QAEjD,IAAI,QAAQ,IAAI,OAAO,IAAI;YACvB,MAAM,iBAAiB,mBAAmB;YAC1C,MAAM,cAAc,CAAC,iCAAiC,EAAE,gBAAgB;YACxE,QAAQ,KAAK,aAAa;YAC1B,eAAe,QAAQ,4BAA4B;YACnD,WAAW,KAAK,gBAAgB;QACpC,OAAO;YACH,MAAM;QACV;IACJ;IAEA,qBACI;;0BACI,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACP,YAAY;oBAAE,OAAO;gBAAI;gBACzB,UAAU;oBAAE,OAAO;gBAAI;gBACvB,SAAS,IAAM,eAAe;gBAC9B,WAAU;gBAIV,OAAO;oBACH,YAAY;oBACZ,WAAW;gBACf;0BAEA,cAAA,6LAAC,gIAAA,CAAA,UAAK;oBACF,OAAO;oBACP,QAAQ;oBACR,WAAU;oBACV,KAAK;oBACL,KAAK;;;;;;;;;;;0BAIb,6LAAC,4LAAA,CAAA,kBAAe;0BACX,6BACG,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACP,WAAU;oBAEV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,SAAS,IAAM,eAAe;8BAE9B,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACP,KAAK;wBACL,WAAU;wBAEV,UAAU;4BACN,QAAQ;gCAAE,SAAS;gCAAG,OAAO;gCAAK,GAAG;4BAAG;4BACxC,SAAS;gCACL,SAAS;gCACT,OAAO;gCACP,GAAG;gCACH,YAAY;oCACR,MAAM;oCACN,WAAW;oCACX,SAAS;gCACb;4BACJ;4BACA,MAAM;gCACF,SAAS;gCACT,OAAO;gCACP,GAAG;gCACH,YAAY;oCAAE,UAAU;gCAAI;4BAChC;wBACJ;wBACA,SAAQ;wBACR,SAAQ;wBACR,MAAK;wBACL,SAAS,CAAC,IAAM,EAAE,eAAe;;0CAEjC,6LAAC;gCACG,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CAEb;;;;;;0CAGD,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCACG,OAAO;wCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;wCAC1C,aAAY;wCACZ,WAAU;;;;;;kDAId,6LAAC;wCACG,SAAS;wCACT,WAAU;kDAGb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjC;GArHM;KAAA;uCAuHS"}}, {"offset": {"line": 4893, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4899, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/hooks/useMediaQuery.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\n\nexport function useMediaQuery(query: string): boolean {\n    const [matches, setMatches] = useState(false);\n\n    useEffect(() => {\n        const media = window.matchMedia(query);\n        if (media.matches !== matches) {\n            setMatches(media.matches);\n        }\n        const listener = () => setMatches(media.matches);\n        window.addEventListener('resize', listener);\n        return () => window.removeEventListener('resize', listener);\n    }, [matches, query]);\n\n    return matches;\n}"], "names": [], "mappings": ";;;AAAA;;;AAEO,SAAS,cAAc,KAAa;;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,MAAM,QAAQ,OAAO,UAAU,CAAC;YAChC,IAAI,MAAM,OAAO,KAAK,SAAS;gBAC3B,WAAW,MAAM,OAAO;YAC5B;YACA,MAAM;oDAAW,IAAM,WAAW,MAAM,OAAO;;YAC/C,OAAO,gBAAgB,CAAC,UAAU;YAClC;2CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACtD;kCAAG;QAAC;QAAS;KAAM;IAEnB,OAAO;AACX;GAdgB"}}, {"offset": {"line": 4932, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4938, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/BestSellers.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport Image from \"next/image\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { ArrowRight } from \"lucide-react\";\r\nimport { useMediaQuery } from \"../../hooks/useMediaQuery\";\r\n\r\ninterface BestSellerProduct {\r\n    id: number;\r\n    name: string;\r\n    description: string;\r\n    image: string;\r\n    price: string;\r\n}\r\n\r\nconst bestSellers: BestSellerProduct[] = [\r\n    {\r\n        id: 1,\r\n        name: \"Spa Chair\",\r\n        description: \"This luxurious spa chair offers ultimate comfort with adjustable positioning and premium materials.\",\r\n        image: \"/images/chair.png\",\r\n        price: \"$599\"\r\n    },\r\n    {\r\n        id: 2,\r\n        name: \"Premium Straight Hair 24\\\"\",\r\n        description: \"Our premium straight hair bundle offers silky smooth texture and long-lasting durability.\",\r\n        image: \"/images/chair2.png\",\r\n        price: \"$399\"\r\n    },\r\n    {\r\n        id: 3,\r\n        name: \"Deluxe Massage Table\",\r\n        description: \"Professional-grade massage table with memory foam padding and adjustable height settings.\",\r\n        image: \"/hero-graphis/facemask.png\",\r\n        price: \"$799\"\r\n    }\r\n];\r\n\r\nconst BestSellers = () => {\r\n    const isMobile = useMediaQuery('(max-width: 768px)');\r\n    const [currentIndex, setCurrentIndex] = useState(0);\r\n    const [direction, setDirection] = useState(0);\r\n    const [isPaused, setIsPaused] = useState(false);\r\n    const intervalRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n    // Auto-advance carousel every second\r\n    useEffect(() => {\r\n        // Clear any existing interval\r\n        if (intervalRef.current) {\r\n            clearInterval(intervalRef.current);\r\n        }\r\n\r\n        // Only set interval if not paused\r\n        if (!isPaused) {\r\n            intervalRef.current = setInterval(() => {\r\n                paginate(1); // Move to next slide\r\n            }, 3000); // Change every 3 seconds for better user experience\r\n        }\r\n\r\n        // Cleanup on component unmount\r\n        return () => {\r\n            if (intervalRef.current) {\r\n                clearInterval(intervalRef.current);\r\n            }\r\n        };\r\n    }, [currentIndex, isPaused]);\r\n\r\n    // Enhanced animation variants with smoother transitions\r\n    const slideVariants = {\r\n        enter: (direction: number) => ({\r\n            x: direction > 0 ? 1000 : -1000,\r\n            opacity: 0,\r\n            scale: 0.95,\r\n            filter: 'blur(4px)',\r\n            position: 'absolute' as const\r\n        }),\r\n        center: {\r\n            zIndex: 1,\r\n            x: 0,\r\n            opacity: 1,\r\n            scale: 1,\r\n            filter: 'blur(0px)',\r\n            position: 'relative' as const\r\n        },\r\n        exit: (direction: number) => ({\r\n            zIndex: 0,\r\n            x: direction < 0 ? 1000 : -1000,\r\n            opacity: 0,\r\n            scale: 0.95,\r\n            filter: 'blur(4px)',\r\n            position: 'absolute' as const\r\n        })\r\n    };\r\n\r\n    const swipeConfidenceThreshold = 10000;\r\n    const swipePower = (offset: number, velocity: number) => {\r\n        return Math.abs(offset) * velocity;\r\n    };\r\n\r\n    const paginate = (newDirection: number) => {\r\n        setDirection(newDirection);\r\n        setCurrentIndex((prevIndex) => (prevIndex + newDirection + bestSellers.length) % bestSellers.length);\r\n    };\r\n\r\n    // Pause auto-rotation when user interacts with carousel\r\n    const pauseAutoRotation = () => {\r\n        setIsPaused(true);\r\n\r\n        // Resume auto-rotation after 5 seconds of inactivity\r\n        setTimeout(() => {\r\n            setIsPaused(false);\r\n        }, 5000);\r\n    };\r\n\r\n    // Background animation variant\r\n    const backgroundVariants = {\r\n        hidden: {\r\n            opacity: 0,\r\n            x: isMobile ? 0 : 100,\r\n            y: isMobile ? 100 : 0\r\n        },\r\n        visible: {\r\n            opacity: 1,\r\n            x: 0,\r\n            y: 0,\r\n            transition: {\r\n                duration: 0.8,\r\n                ease: [0.25, 0.4, 0.3, 1.1]\r\n            }\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"relative w-full overflow-hidden\">\r\n            {/* Gradient overlay background */}\r\n            <motion.div\r\n                variants={backgroundVariants}\r\n                initial=\"hidden\"\r\n                animate=\"visible\"\r\n                className=\"absolute h-[120%] inset-0 bg-gradient-to-b md:bg-gradient-to-r from-transparent via-[#EDEDED]/30 to-[#EDEDED] -z-10\"\r\n            />\r\n\r\n            <div className=\"container mx-auto px-4 sm:px-6 md:px-8 py-6 sm:py-12 md:py-20\">\r\n                <div className=\"relative\">\r\n                    {/* Navigation Buttons removed as requested */}\r\n\r\n                    {/* Carousel Content */}\r\n                    <div className=\"relative min-h-[400px] md:min-h-[500px]\">\r\n                        <AnimatePresence\r\n                            initial={false}\r\n                            custom={direction}\r\n                            mode=\"wait\"\r\n                        >\r\n                            <motion.div\r\n                                key={currentIndex}\r\n                                custom={direction}\r\n                                variants={slideVariants}\r\n                                initial=\"enter\"\r\n                                animate=\"center\"\r\n                                exit=\"exit\"\r\n                                transition={{\r\n                                    x: { type: \"spring\", stiffness: 300, damping: 30 },\r\n                                    opacity: { duration: 0.5 },\r\n                                    scale: { duration: 0.5 },\r\n                                    filter: { duration: 0.5 },\r\n                                    position: { duration: 0 }\r\n                                }}\r\n                                drag=\"x\"\r\n                                dragConstraints={{ left: 0, right: 0 }}\r\n                                dragElastic={1}\r\n                                onDragStart={() => pauseAutoRotation()}\r\n                                onDragEnd={(_, { offset, velocity }) => {\r\n                                    const swipe = swipePower(offset.x, velocity.x);\r\n                                    if (swipe < -swipeConfidenceThreshold) {\r\n                                        paginate(1);\r\n                                    } else if (swipe > swipeConfidenceThreshold) {\r\n                                        paginate(-1);\r\n                                    }\r\n                                }}\r\n                                className=\"flex flex-col md:flex-row md:space-x-12 lg:space-x-24 space-y-8 md:space-y-0 items-center justify-center w-full\"\r\n                                style={{\r\n                                    willChange: \"transform, opacity\"\r\n                                }}\r\n                            >\r\n                                {/* Image container with animation */}\r\n                                <div className=\"flex-shrink-0 flex items-center justify-center rounded-[16px] md:rounded-[20px]\r\n                                      relative before:absolute before:inset-0 before:p-[1.5px]\r\n                                      before:rounded-[16px] md:before:rounded-[20px] before:bg-gradient-to-tr\r\n                                      before:from-[#FF69B4] before:via-[#87CEFA] before:to-[#FF69B4]\r\n                                      after:absolute after:inset-[1.5px] after:rounded-[15px] md:after:rounded-[18px]\r\n                                      after:bg-gradient-to-br after:from-white after:to-[#fafafa]\r\n                                      w-[220px] sm:w-[260px] md:w-[300px] aspect-square\r\n                                      p-2 sm:p-4 md:p-8\"\r\n                                >\r\n                                    <div className=\"relative w-full h-full z-10\">\r\n                                        <Image\r\n                                            src={bestSellers[currentIndex].image}\r\n                                            alt={bestSellers[currentIndex].name}\r\n                                            fill\r\n                                            className=\"object-contain p-3\"\r\n                                            sizes=\"(max-width: 640px) 220px, (max-width: 768px) 260px, 300px\"\r\n                                            priority\r\n                                        />\r\n                                    </div>\r\n                                </div>\r\n\r\n                                {/* Content section */}\r\n                                <div className=\"flex flex-col justify-center space-y-4 w-full md:w-auto px-10 md:px-2 gap-4\">\r\n                                    <div className=\"flex items-start flex-col space-y-2.5\">\r\n                                        <motion.div\r\n                                            className=\"text-2xl sm:text-2xl md:text-3xl font-semibold tracking-tight\"\r\n                                            initial={{ opacity: 0, y: 20 }}\r\n                                            animate={{ opacity: 1, y: 0 }}\r\n                                            transition={{ duration: 0.3 }}\r\n                                        >\r\n                                            {bestSellers[currentIndex].name}\r\n                                        </motion.div>\r\n                                        <motion.div\r\n                                            className=\"w-28 sm:w-30 md:w-[150px] h-[1px] bg-gradient-to-r from-[#9d9d9d] to-transparent\"\r\n                                            initial={{ scaleX: 0 }}\r\n                                            animate={{ scaleX: 1 }}\r\n                                            transition={{ duration: 0.3, delay: 0.1 }}\r\n                                        />\r\n                                        <motion.div\r\n                                            className=\"w-full max-w-[280px] sm:w-80 md:w-70 text-sm sm:text-sm md:text-base text-gray-600/90 leading-relaxed\"\r\n                                            initial={{ opacity: 0, y: 20 }}\r\n                                            animate={{ opacity: 1, y: 0 }}\r\n                                            transition={{ duration: 0.3, delay: 0.2 }}\r\n                                        >\r\n                                            {bestSellers[currentIndex].description}\r\n                                        </motion.div>\r\n                                        <motion.div\r\n                                            className=\"text-xl font-semibold text-[#FF69B4]\"\r\n                                            initial={{ opacity: 0, y: 20 }}\r\n                                            animate={{ opacity: 1, y: 0 }}\r\n                                            transition={{ duration: 0.3, delay: 0.3 }}\r\n                                        >\r\n                                            {bestSellers[currentIndex].price}\r\n                                        </motion.div>\r\n                                    </div>\r\n\r\n                                    <motion.button\r\n                                        whileHover={{ scale: 1.02 }}\r\n                                        whileTap={{ scale: 0.95 }}\r\n                                        initial={{ opacity: 0, y: 20 }}\r\n                                        animate={{ opacity: 1, y: 0 }}\r\n                                        transition={{ duration: 0.3, delay: 0.4 }}\r\n                                        className=\"font-medium bg-gradient-to-tr from-[#FF69B4] to-[#1E90FF] text-[#fff]\r\n                                             text-sm tracking-wide rounded-full py-2.5 px-5\r\n                                             flex items-center w-44 sm:w-40 md:w-44 gap-2\"\r\n                                    >\r\n                                        Discover More\r\n                                        <ArrowRight size={16} />\r\n                                    </motion.button>\r\n                                </div>\r\n                            </motion.div>\r\n                        </AnimatePresence>\r\n                    </div>\r\n\r\n                    {/* Dots indicator with progress */}\r\n                    <div className=\"flex flex-col items-center gap-2 mt-6\">\r\n                        <div className=\"flex justify-center gap-2\">\r\n                            {bestSellers.map((_, index) => (\r\n                                <motion.button\r\n                                    key={index}\r\n                                    onClick={() => {\r\n                                        pauseAutoRotation();\r\n                                        setDirection(index > currentIndex ? 1 : -1);\r\n                                        setCurrentIndex(index);\r\n                                    }}\r\n                                    whileHover={{ scale: 1.2 }}\r\n                                    whileTap={{ scale: 0.9 }}\r\n                                    className={`w-2 h-2 rounded-full transition-all ${index === currentIndex ? 'bg-[#FF69B4] w-4' : 'bg-gray-300'\r\n                                        }`}\r\n                                />\r\n                            ))}\r\n                        </div>\r\n\r\n                        {/* Progress indicator */}\r\n                        {!isPaused && (\r\n                            <motion.div\r\n                                className=\"w-16 h-0.5 bg-gray-200 mt-2 overflow-hidden\"\r\n                                initial={{ opacity: 0.6 }}\r\n                                animate={{ opacity: 1 }}\r\n                            >\r\n                                <motion.div\r\n                                    className=\"h-full bg-[#FF69B4]\"\r\n                                    initial={{ width: \"0%\" }}\r\n                                    animate={{ width: \"100%\" }}\r\n                                    transition={{\r\n                                        duration: 3,\r\n                                        ease: \"linear\",\r\n                                        repeat: Infinity,\r\n                                        repeatType: \"loop\"\r\n                                    }}\r\n                                />\r\n                            </motion.div>\r\n                        )}\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default BestSellers;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAGA;AAFA;AAAA;AACA;;;AAJA;;;;;;AAeA,MAAM,cAAmC;IACrC;QACI,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;IACX;IACA;QACI,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;IACX;IACA;QACI,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;IACX;CACH;AAED,MAAM,cAAc;;IAChB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD,EAAE;IAC/B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAElD,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,8BAA8B;YAC9B,IAAI,YAAY,OAAO,EAAE;gBACrB,cAAc,YAAY,OAAO;YACrC;YAEA,kCAAkC;YAClC,IAAI,CAAC,UAAU;gBACX,YAAY,OAAO,GAAG;6CAAY;wBAC9B,SAAS,IAAI,qBAAqB;oBACtC;4CAAG,OAAO,oDAAoD;YAClE;YAEA,+BAA+B;YAC/B;yCAAO;oBACH,IAAI,YAAY,OAAO,EAAE;wBACrB,cAAc,YAAY,OAAO;oBACrC;gBACJ;;QACJ;gCAAG;QAAC;QAAc;KAAS;IAE3B,wDAAwD;IACxD,MAAM,gBAAgB;QAClB,OAAO,CAAC,YAAsB,CAAC;gBAC3B,GAAG,YAAY,IAAI,OAAO,CAAC;gBAC3B,SAAS;gBACT,OAAO;gBACP,QAAQ;gBACR,UAAU;YACd,CAAC;QACD,QAAQ;YACJ,QAAQ;YACR,GAAG;YACH,SAAS;YACT,OAAO;YACP,QAAQ;YACR,UAAU;QACd;QACA,MAAM,CAAC,YAAsB,CAAC;gBAC1B,QAAQ;gBACR,GAAG,YAAY,IAAI,OAAO,CAAC;gBAC3B,SAAS;gBACT,OAAO;gBACP,QAAQ;gBACR,UAAU;YACd,CAAC;IACL;IAEA,MAAM,2BAA2B;IACjC,MAAM,aAAa,CAAC,QAAgB;QAChC,OAAO,KAAK,GAAG,CAAC,UAAU;IAC9B;IAEA,MAAM,WAAW,CAAC;QACd,aAAa;QACb,gBAAgB,CAAC,YAAc,CAAC,YAAY,eAAe,YAAY,MAAM,IAAI,YAAY,MAAM;IACvG;IAEA,wDAAwD;IACxD,MAAM,oBAAoB;QACtB,YAAY;QAEZ,qDAAqD;QACrD,WAAW;YACP,YAAY;QAChB,GAAG;IACP;IAEA,+BAA+B;IAC/B,MAAM,qBAAqB;QACvB,QAAQ;YACJ,SAAS;YACT,GAAG,WAAW,IAAI;YAClB,GAAG,WAAW,MAAM;QACxB;QACA,SAAS;YACL,SAAS;YACT,GAAG;YACH,GAAG;YACH,YAAY;gBACR,UAAU;gBACV,MAAM;oBAAC;oBAAM;oBAAK;oBAAK;iBAAI;YAC/B;QACJ;IACJ;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BAEX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACP,UAAU;gBACV,SAAQ;gBACR,SAAQ;gBACR,WAAU;;;;;;0BAGd,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;;sCAIX,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC,4LAAA,CAAA,kBAAe;gCACZ,SAAS;gCACT,QAAQ;gCACR,MAAK;0CAEL,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAEP,QAAQ;oCACR,UAAU;oCACV,SAAQ;oCACR,SAAQ;oCACR,MAAK;oCACL,YAAY;wCACR,GAAG;4CAAE,MAAM;4CAAU,WAAW;4CAAK,SAAS;wCAAG;wCACjD,SAAS;4CAAE,UAAU;wCAAI;wCACzB,OAAO;4CAAE,UAAU;wCAAI;wCACvB,QAAQ;4CAAE,UAAU;wCAAI;wCACxB,UAAU;4CAAE,UAAU;wCAAE;oCAC5B;oCACA,MAAK;oCACL,iBAAiB;wCAAE,MAAM;wCAAG,OAAO;oCAAE;oCACrC,aAAa;oCACb,aAAa,IAAM;oCACnB,WAAW,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE;wCAC/B,MAAM,QAAQ,WAAW,OAAO,CAAC,EAAE,SAAS,CAAC;wCAC7C,IAAI,QAAQ,CAAC,0BAA0B;4CACnC,SAAS;wCACb,OAAO,IAAI,QAAQ,0BAA0B;4CACzC,SAAS,CAAC;wCACd;oCACJ;oCACA,WAAU;oCACV,OAAO;wCACH,YAAY;oCAChB;;sDAGA,6LAAC;4CAAI,WAAU;sDASX,cAAA,6LAAC;gDAAI,WAAU;0DACX,cAAA,6LAAC,gIAAA,CAAA,UAAK;oDACF,KAAK,WAAW,CAAC,aAAa,CAAC,KAAK;oDACpC,KAAK,WAAW,CAAC,aAAa,CAAC,IAAI;oDACnC,IAAI;oDACJ,WAAU;oDACV,OAAM;oDACN,QAAQ;;;;;;;;;;;;;;;;sDAMpB,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAI,WAAU;;sEACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DACP,WAAU;4DACV,SAAS;gEAAE,SAAS;gEAAG,GAAG;4DAAG;4DAC7B,SAAS;gEAAE,SAAS;gEAAG,GAAG;4DAAE;4DAC5B,YAAY;gEAAE,UAAU;4DAAI;sEAE3B,WAAW,CAAC,aAAa,CAAC,IAAI;;;;;;sEAEnC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DACP,WAAU;4DACV,SAAS;gEAAE,QAAQ;4DAAE;4DACrB,SAAS;gEAAE,QAAQ;4DAAE;4DACrB,YAAY;gEAAE,UAAU;gEAAK,OAAO;4DAAI;;;;;;sEAE5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DACP,WAAU;4DACV,SAAS;gEAAE,SAAS;gEAAG,GAAG;4DAAG;4DAC7B,SAAS;gEAAE,SAAS;gEAAG,GAAG;4DAAE;4DAC5B,YAAY;gEAAE,UAAU;gEAAK,OAAO;4DAAI;sEAEvC,WAAW,CAAC,aAAa,CAAC,WAAW;;;;;;sEAE1C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DACP,WAAU;4DACV,SAAS;gEAAE,SAAS;gEAAG,GAAG;4DAAG;4DAC7B,SAAS;gEAAE,SAAS;gEAAG,GAAG;4DAAE;4DAC5B,YAAY;gEAAE,UAAU;gEAAK,OAAO;4DAAI;sEAEvC,WAAW,CAAC,aAAa,CAAC,KAAK;;;;;;;;;;;;8DAIxC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oDACV,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,UAAU;wDAAE,OAAO;oDAAK;oDACxB,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,YAAY;wDAAE,UAAU;wDAAK,OAAO;oDAAI;oDACxC,WAAU;;wDAGb;sEAEG,6LAAC,qNAAA,CAAA,aAAU;4DAAC,MAAM;;;;;;;;;;;;;;;;;;;mCAlGrB;;;;;;;;;;;;;;;sCA0GjB,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;8CACV,YAAY,GAAG,CAAC,CAAC,GAAG,sBACjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CAEV,SAAS;gDACL;gDACA,aAAa,QAAQ,eAAe,IAAI,CAAC;gDACzC,gBAAgB;4CACpB;4CACA,YAAY;gDAAE,OAAO;4CAAI;4CACzB,UAAU;gDAAE,OAAO;4CAAI;4CACvB,WAAW,CAAC,oCAAoC,EAAE,UAAU,eAAe,qBAAqB,eAC1F;2CATD;;;;;;;;;;gCAehB,CAAC,0BACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;oCAAI;oCACxB,SAAS;wCAAE,SAAS;oCAAE;8CAEtB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACP,WAAU;wCACV,SAAS;4CAAE,OAAO;wCAAK;wCACvB,SAAS;4CAAE,OAAO;wCAAO;wCACzB,YAAY;4CACR,UAAU;4CACV,MAAM;4CACN,QAAQ;4CACR,YAAY;wCAChB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpC;GAzQM;;QACe,gIAAA,CAAA,gBAAa;;;KAD5B;uCA2QS"}}, {"offset": {"line": 5414, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5420, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/services/categoryService.ts"], "sourcesContent": ["import { ID, Query, Models } from \"appwrite\";\nimport { databases, appwriteConfig } from \"@/src/lib/appwrite\";\n\nexport interface AppwriteCategory extends Models.Document {\n  name: string;\n  icon: string;\n}\n\nexport interface Category {\n  id: string;\n  name: string;\n  icon: string;\n  imageSrc: string;\n}\n\n// Convert Appwrite category to local format\nexport const appwriteToLocalCategory = (\n  category: AppwriteCategory\n): Category => {\n  return {\n    id: category.$id, // Use the Appwrite document ID\n    name: category.name,\n    icon: category.icon || \"✨\",\n    imageSrc: `/icons/${category.name.toLowerCase().replace(/\\s+/g, \"-\")}.png`,\n  };\n};\n\n// Convert local category to Appwrite format\nexport const localToAppwriteCategory = (\n  category: Category\n): Omit<AppwriteCategory, \"$id\"> => {\n  return {\n    name: category.name,\n    icon: category.icon,\n  };\n};\n\n// Fetch all categories from Appwrite\nexport const fetchCategories = async (): Promise<Category[]> => {\n  try {\n    const response = await databases.listDocuments(\n      appwriteConfig.databaseId,\n      appwriteConfig.categoriesCollectionId,\n      [Query.orderAsc(\"name\")]\n    );\n\n    return response.documents.map((doc) =>\n      appwriteToLocalCategory(doc as unknown as AppwriteCategory)\n    );\n  } catch (error) {\n    console.error(\"Error fetching categories:\", error);\n    return [];\n  }\n};\n\n// Add a new category to Appwrite\nexport const addCategory = async (\n  category: Omit<Category, \"id\" | \"imageSrc\">\n): Promise<Category | null> => {\n  try {\n    // Check if category already exists\n    const existingCategories = await databases.listDocuments(\n      appwriteConfig.databaseId,\n      appwriteConfig.categoriesCollectionId,\n      [Query.equal(\"name\", category.name)]\n    );\n\n    if (existingCategories.documents.length > 0) {\n      console.log(\"Category already exists:\", category.name);\n      return appwriteToLocalCategory(\n        existingCategories.documents[0] as unknown as AppwriteCategory\n      );\n    }\n\n    // Create new category\n    const newCategory = await databases.createDocument(\n      appwriteConfig.databaseId,\n      appwriteConfig.categoriesCollectionId,\n      ID.unique(),\n      {\n        name: category.name,\n        icon: category.icon,\n      }\n    );\n\n    return appwriteToLocalCategory(newCategory as unknown as AppwriteCategory);\n  } catch (error) {\n    console.error(\"Error adding category:\", error);\n    return null;\n  }\n};\n\n// Update an existing category in Appwrite\nexport const updateCategory = async (\n  id: string,\n  category: Partial<Omit<Category, \"id\" | \"imageSrc\">>\n): Promise<Category | null> => {\n  try {\n    const updatedCategory = await databases.updateDocument(\n      appwriteConfig.databaseId,\n      appwriteConfig.categoriesCollectionId,\n      id,\n      {\n        name: category.name,\n        icon: category.icon,\n      }\n    );\n\n    return appwriteToLocalCategory(\n      updatedCategory as unknown as AppwriteCategory\n    );\n  } catch (error) {\n    console.error(\"Error updating category:\", error);\n    return null;\n  }\n};\n\n// Delete a category from Appwrite\nexport const deleteCategory = async (id: string): Promise<boolean> => {\n  console.log(`Attempting to delete category with ID: ${id}`);\n  console.log(`Database ID: ${appwriteConfig.databaseId}`);\n  console.log(`Collection ID: ${appwriteConfig.categoriesCollectionId}`);\n\n  try {\n    await databases.deleteDocument(\n      appwriteConfig.databaseId,\n      appwriteConfig.categoriesCollectionId,\n      id\n    );\n\n    console.log(`Successfully deleted category with ID: ${id}`);\n    return true;\n  } catch (error) {\n    console.error(\"Error deleting category:\", error);\n    return false;\n  }\n};\n\n// Upload initial categories to Appwrite\nexport const uploadInitialCategories = async (\n  categories: Category[]\n): Promise<void> => {\n  try {\n    for (const category of categories) {\n      await addCategory({\n        name: category.name,\n        icon: category.icon,\n      });\n    }\n    console.log(\"Initial categories uploaded successfully\");\n  } catch (error) {\n    console.error(\"Error uploading initial categories:\", error);\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAeO,MAAM,0BAA0B,CACrC;IAEA,OAAO;QACL,IAAI,SAAS,GAAG;QAChB,MAAM,SAAS,IAAI;QACnB,MAAM,SAAS,IAAI,IAAI;QACvB,UAAU,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC;IAC5E;AACF;AAGO,MAAM,0BAA0B,CACrC;IAEA,OAAO;QACL,MAAM,SAAS,IAAI;QACnB,MAAM,SAAS,IAAI;IACrB;AACF;AAGO,MAAM,kBAAkB;IAC7B,IAAI;QACF,MAAM,WAAW,MAAM,yHAAA,CAAA,YAAS,CAAC,aAAa,CAC5C,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,yHAAA,CAAA,iBAAc,CAAC,sBAAsB,EACrC;YAAC,iJAAA,CAAA,QAAK,CAAC,QAAQ,CAAC;SAAQ;QAG1B,OAAO,SAAS,SAAS,CAAC,GAAG,CAAC,CAAC,MAC7B,wBAAwB;IAE5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,EAAE;IACX;AACF;AAGO,MAAM,cAAc,OACzB;IAEA,IAAI;QACF,mCAAmC;QACnC,MAAM,qBAAqB,MAAM,yHAAA,CAAA,YAAS,CAAC,aAAa,CACtD,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,yHAAA,CAAA,iBAAc,CAAC,sBAAsB,EACrC;YAAC,iJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,QAAQ,SAAS,IAAI;SAAE;QAGtC,IAAI,mBAAmB,SAAS,CAAC,MAAM,GAAG,GAAG;YAC3C,QAAQ,GAAG,CAAC,4BAA4B,SAAS,IAAI;YACrD,OAAO,wBACL,mBAAmB,SAAS,CAAC,EAAE;QAEnC;QAEA,sBAAsB;QACtB,MAAM,cAAc,MAAM,yHAAA,CAAA,YAAS,CAAC,cAAc,CAChD,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,yHAAA,CAAA,iBAAc,CAAC,sBAAsB,EACrC,iJAAA,CAAA,KAAE,CAAC,MAAM,IACT;YACE,MAAM,SAAS,IAAI;YACnB,MAAM,SAAS,IAAI;QACrB;QAGF,OAAO,wBAAwB;IACjC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;IACT;AACF;AAGO,MAAM,iBAAiB,OAC5B,IACA;IAEA,IAAI;QACF,MAAM,kBAAkB,MAAM,yHAAA,CAAA,YAAS,CAAC,cAAc,CACpD,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,yHAAA,CAAA,iBAAc,CAAC,sBAAsB,EACrC,IACA;YACE,MAAM,SAAS,IAAI;YACnB,MAAM,SAAS,IAAI;QACrB;QAGF,OAAO,wBACL;IAEJ,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF;AAGO,MAAM,iBAAiB,OAAO;IACnC,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,IAAI;IAC1D,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,yHAAA,CAAA,iBAAc,CAAC,UAAU,EAAE;IACvD,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,yHAAA,CAAA,iBAAc,CAAC,sBAAsB,EAAE;IAErE,IAAI;QACF,MAAM,yHAAA,CAAA,YAAS,CAAC,cAAc,CAC5B,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,yHAAA,CAAA,iBAAc,CAAC,sBAAsB,EACrC;QAGF,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,IAAI;QAC1D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF;AAGO,MAAM,0BAA0B,OACrC;IAEA,IAAI;QACF,KAAK,MAAM,YAAY,WAAY;YACjC,MAAM,YAAY;gBAChB,MAAM,SAAS,IAAI;gBACnB,MAAM,SAAS,IAAI;YACrB;QACF;QACA,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;IACvD;AACF"}}, {"offset": {"line": 5520, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5526, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/CategoriesModal.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useRouter } from 'next/navigation';\nimport Image from 'next/image';\nimport { Grid } from 'lucide-react';\nimport { fetchCategories, Category } from '@/src/services/categoryService';\nimport SpinningLoader from './SpinningLoader';\n\ninterface CategoriesModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst CategoriesModal: React.FC<CategoriesModalProps> = ({ isOpen, onClose }) => {\n  const router = useRouter();\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    if (isOpen) {\n      const loadCategories = async () => {\n        try {\n          setLoading(true);\n          const fetchedCategories = await fetchCategories();\n          setCategories(fetchedCategories);\n        } catch (error) {\n          console.error('Error loading categories:', error);\n        } finally {\n          setLoading(false);\n        }\n      };\n\n      loadCategories();\n    }\n  }, [isOpen]);\n\n  const handleCategoryClick = (categoryId: string) => {\n    router.push(`/shop?category=${categoryId}`);\n    onClose();\n  };\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <div className=\"fixed inset-0 flex items-center justify-center z-[9999]\">\n          {/* Backdrop */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 0.2 }}\n            onClick={onClose}\n            className=\"absolute inset-0 bg-black/60 backdrop-blur-sm pointer-events-auto\"\n          />\n\n          {/* Modal */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9, y: 20 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.9, y: 20 }}\n            transition={{\n              type: \"spring\",\n              damping: 25,\n              stiffness: 300\n            }}\n            className=\"relative w-[95%] sm:w-[90%] md:w-[85%] max-w-2xl\n                      bg-white rounded-2xl shadow-2xl z-[10000] overflow-hidden max-h-[90vh] pointer-events-auto\"\n            onClick={(e) => e.stopPropagation()}\n          >\n            {/* Header */}\n            <div className=\"p-3 sm:p-4 md:p-6 border-b border-gray-100 flex justify-between items-center bg-gradient-to-r from-[#f8f9fa] to-[#f1f3f5]\">\n              <div className=\"flex items-center gap-2 sm:gap-3\">\n                <div className=\"w-7 h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 bg-white rounded-full flex items-center justify-center shadow-sm\">\n                  <Grid size={16} className=\"text-[#1E90FF] sm:hidden\" />\n                  <Grid size={18} className=\"text-[#1E90FF] hidden sm:block md:hidden\" />\n                  <Grid size={20} className=\"text-[#1E90FF] hidden md:block\" />\n                </div>\n                <h2 className=\"text-lg sm:text-xl md:text-2xl font-bold bg-gradient-to-r from-[#1E90FF] to-[#FF69B4] bg-clip-text text-transparent\">\n                  All Categories\n                </h2>\n              </div>\n              <motion.button\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n                onClick={onClose}\n                className=\"p-1.5 sm:p-2 rounded-full hover:bg-white/80 transition-colors\"\n              >\n                <svg\n                  className=\"w-4 h-4 sm:w-5 sm:h-5 text-gray-500\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth=\"2\"\n                    d=\"M6 18L18 6M6 6l12 12\"\n                  />\n                </svg>\n              </motion.button>\n            </div>\n\n            {/* Categories Grid */}\n            <div className=\"p-3 sm:p-4 md:p-6 overflow-y-auto max-h-[40vh] sm:max-h-[50vh]\">\n              {loading ? (\n                <motion.div\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                  className=\"flex justify-center items-center py-10\"\n                >\n                  <div className=\"bg-white/80 backdrop-blur-sm p-4 rounded-lg\">\n                    <SpinningLoader size=\"medium\" text=\"Loading categories...\" />\n                  </div>\n                </motion.div>\n              ) : (\n                <div className=\"grid grid-cols-2 sm:grid-cols-3 gap-2 sm:gap-4\">\n                  {categories.map((category) => (\n                    <motion.div\n                      key={category.id}\n                      whileHover={{ scale: 1.03, y: -5 }}\n                      whileTap={{ scale: 0.97 }}\n                      onClick={() => handleCategoryClick(category.id)}\n                      className=\"flex flex-col items-center justify-center p-2 sm:p-4 rounded-xl\n                                bg-gradient-to-br from-gray-50 to-gray-100 hover:shadow-md\n                                cursor-pointer transition-all border border-gray-100 hover:border-[#1E90FF]/30\"\n                    >\n                      <div className=\"w-12 h-12 sm:w-16 sm:h-16 md:w-20 md:h-20 relative mb-2 sm:mb-3\n                                    bg-white rounded-full p-2 sm:p-3 shadow-sm\">\n                        <Image\n                          src={category.imageSrc}\n                          alt={category.name}\n                          fill\n                          className=\"object-contain p-1 sm:p-2\"\n                        />\n                      </div>\n                      <span className=\"text-xs sm:text-sm md:text-base font-medium text-center\">\n                        {category.name}\n                      </span>\n                    </motion.div>\n                  ))}\n                </div>\n              )}\n            </div>\n\n            {/* Footer */}\n            <div className=\"p-3 sm:p-4 md:p-6 border-t border-gray-100 flex justify-between items-center bg-gradient-to-r from-[#f8f9fa] to-[#f1f3f5]\">\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={onClose}\n                className=\"px-2 sm:px-3 md:px-4 py-1.5 sm:py-2 border border-gray-200 text-gray-600\n                          rounded-full text-xs sm:text-sm font-medium hover:bg-white hover:shadow-sm transition-all\"\n              >\n                Close\n              </motion.button>\n\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => router.push('/shop')}\n                className=\"px-2 sm:px-3 md:px-4 py-1.5 sm:py-2 bg-gradient-to-r from-[#1E90FF] to-[#FF69B4] text-white\n                          rounded-full text-xs sm:text-sm font-medium hover:shadow-md transition-all\"\n              >\n                <span className=\"hidden sm:inline\">View All Products</span>\n                <span className=\"sm:hidden\">View All</span>\n              </motion.button>\n            </div>\n          </motion.div>\n        </div>\n      )}\n    </AnimatePresence>\n  );\n};\n\nexport default CategoriesModal;\n"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AAEA;AACA;AALA;AAAA;AAGA;;;AALA;;;;;;;;AAcA,MAAM,kBAAkD,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;;IAC1E,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,QAAQ;gBACV,MAAM;gEAAiB;wBACrB,IAAI;4BACF,WAAW;4BACX,MAAM,oBAAoB,MAAM,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;4BAC9C,cAAc;wBAChB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,6BAA6B;wBAC7C,SAAU;4BACR,WAAW;wBACb;oBACF;;gBAEA;YACF;QACF;oCAAG;QAAC;KAAO;IAEX,MAAM,sBAAsB,CAAC;QAC3B,OAAO,IAAI,CAAC,CAAC,eAAe,EAAE,YAAY;QAC1C;IACF;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,wBACC,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,SAAS;oBACT,WAAU;;;;;;8BAIZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACzC,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAG,GAAG;oBAAE;oBACtC,MAAM;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACtC,YAAY;wBACV,MAAM;wBACN,SAAS;wBACT,WAAW;oBACb;oBACA,WAAU;oBAEV,SAAS,CAAC,IAAM,EAAE,eAAe;;sCAGjC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,4MAAA,CAAA,OAAI;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC1B,6LAAC,4MAAA,CAAA,OAAI;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC1B,6LAAC,4MAAA,CAAA,OAAI;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;;sDAE5B,6LAAC;4CAAG,WAAU;sDAAsH;;;;;;;;;;;;8CAItI,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAI;oCACvB,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC;wCACC,WAAU;wCACV,MAAK;wCACL,QAAO;wCACP,SAAQ;wCACR,OAAM;kDAEN,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAY;4CACZ,GAAE;;;;;;;;;;;;;;;;;;;;;;sCAOV,6LAAC;4BAAI,WAAU;sCACZ,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,8IAAA,CAAA,UAAc;wCAAC,MAAK;wCAAS,MAAK;;;;;;;;;;;;;;;qDAIvC,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,YAAY;4CAAE,OAAO;4CAAM,GAAG,CAAC;wCAAE;wCACjC,UAAU;4CAAE,OAAO;wCAAK;wCACxB,SAAS,IAAM,oBAAoB,SAAS,EAAE;wCAC9C,WAAU;;0DAIV,6LAAC;gDAAI,WAAU;0DAEb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK,SAAS,QAAQ;oDACtB,KAAK,SAAS,IAAI;oDAClB,IAAI;oDACJ,WAAU;;;;;;;;;;;0DAGd,6LAAC;gDAAK,WAAU;0DACb,SAAS,IAAI;;;;;;;uCAlBX,SAAS,EAAE;;;;;;;;;;;;;;;sCA2B1B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS;oCACT,WAAU;8CAEX;;;;;;8CAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,WAAU;;sDAGV,6LAAC;4CAAK,WAAU;sDAAmB;;;;;;sDACnC,6LAAC;4CAAK,WAAU;sDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5C;GAjKM;;QACW,qIAAA,CAAA,YAAS;;;KADpB;uCAmKS"}}, {"offset": {"line": 5890, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5896, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/AllCategoriesButton.tsx"], "sourcesContent": ["\"use client\"\nimport { useState } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { Grid } from \"lucide-react\";\nimport CategoriesModal from \"./CategoriesModal\";\n\nconst AllCategoriesButton = () => {\n    const [isCategoriesModalOpen, setIsCategoriesModalOpen] = useState(false);\n\n    return (\n        <>\n            {/* Categories Modal - Ensure it's clickable when open */}\n            <CategoriesModal\n                isOpen={isCategoriesModalOpen}\n                onClose={() => setIsCategoriesModalOpen(false)}\n            />\n\n            {/* All Categories Button */}\n            <div className=\"mt-6 sm:mt-8 mb-8 sm:mb-12 w-full flex justify-center\">\n                <motion.button\n                    onClick={() => setIsCategoriesModalOpen(true)}\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"flex items-center gap-1.5 sm:gap-2 px-4 sm:px-6 py-2 sm:py-3 bg-gradient-to-r from-[#1E90FF] to-[#FF69B4]\n                              text-white rounded-full text-sm sm:text-base font-medium shadow-md hover:shadow-lg transition-all mx-auto\"\n                >\n                    <Grid size={16} className=\"sm:hidden\" />\n                    <Grid size={18} className=\"hidden sm:block\" /> \n                    <span>All Categories</span>\n                </motion.button>\n            </div>\n        </>\n    );\n};\n\nexport default AllCategoriesButton;\n"], "names": [], "mappings": ";;;;AACA;AAGA;AAFA;AACA;;;AAHA;;;;;AAMA,MAAM,sBAAsB;;IACxB,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,qBACI;;0BAEI,6LAAC,+IAAA,CAAA,UAAe;gBACZ,QAAQ;gBACR,SAAS,IAAM,yBAAyB;;;;;;0BAI5C,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oBACV,SAAS,IAAM,yBAAyB;oBACxC,YAAY;wBAAE,OAAO;oBAAK;oBAC1B,UAAU;wBAAE,OAAO;oBAAK;oBACxB,WAAU;;sCAGV,6LAAC,4MAAA,CAAA,OAAI;4BAAC,MAAM;4BAAI,WAAU;;;;;;sCAC1B,6LAAC,4MAAA,CAAA,OAAI;4BAAC,MAAM;4BAAI,WAAU;;;;;;sCAC1B,6LAAC;sCAAK;;;;;;;;;;;;;;;;;;;AAK1B;GA3BM;KAAA;uCA6BS"}}, {"offset": {"line": 5981, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}