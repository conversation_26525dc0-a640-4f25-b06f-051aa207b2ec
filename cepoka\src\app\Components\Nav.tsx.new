"use client";

import { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";
import { Search } from "lucide-react";

const Nav = () => {
  // =============== STATE MANAGEMENT ===============
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeLink, setActiveLink] = useState("Home");
  const [isVisible, setIsVisible] = useState(true);
  const [isMounted, setIsMounted] = useState(false);

  // =============== HOOKS ===============
  const router = useRouter();
  const pathname = usePathname();

  // =============== ANIMATIONS ===============
  const menuItemVariants = {
    open: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5,
        ease: [0.4, 0, 0.2, 1],
      },
    }),
    closed: (i: number) => ({
      opacity: 0,
      y: -20,
      transition: {
        delay: i * 0.05,
        duration: 0.3,
        ease: [0.4, 0, 0.2, 1],
      },
    }),
  };

  // =============== LIFECYCLE HOOKS ===============
  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isMounted || typeof window === 'undefined') return;

    const handleScroll = () => {
      const scrollPosition = window?.scrollY;
      setIsVisible(scrollPosition > 300); // Show the navbar after scrolling 300px
    };

    if (pathname === "/") {
      // Add scroll event listener for the home page
      setIsVisible(false); // Initially hide the navbar
      window?.addEventListener("scroll", handleScroll);
      return () => window?.removeEventListener("scroll", handleScroll);
    } else {
      setIsVisible(true); // Show the navbar immediately for other pages
    }

    // Clean up the event listener
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [pathname, isMounted]);

  // Updated click handler with better area detection
  useEffect(() => {
    const handleClickAway = (event: MouseEvent) => {
      if (!isMenuOpen) return;

      const target = event.target as HTMLElement;
      const mobileMenu = document.getElementById('mobile-menu-container');
      const hamburgerButton = document.getElementById('hamburger-button');
      const searchForm = document.getElementById('mobile-search-form');

      // Check if click is within any of our menu components
      const isClickInMenu = mobileMenu?.contains(target);
      const isClickOnButton = hamburgerButton?.contains(target);
      const isClickInSearch = searchForm?.contains(target);
      const isClickOnOverlay = target.classList.contains('menu-overlay');

      // Only close if clicking outside all menu components and on overlay
      if ((!isClickInMenu && !isClickOnButton && !isClickInSearch) && isClickOnOverlay) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickAway);
    return () => document.removeEventListener('mousedown', handleClickAway);
  }, [isMenuOpen]);

  // =============== EVENT HANDLERS ===============
  const handleNavClick = (link: string) => {
    if (!isMounted || typeof window === 'undefined') return;

    setActiveLink(link);
    setIsMenuOpen(false);

    // Navigate to the appropriate page
    switch (link) {
      case "Home":
        router.push("/");
        break;
      case "Shop":
        router.push("/shop");
        break;
      case "Contact":
        router.push("/contact");
        break;
      case "About":
        router.push("/about");
        break;
      default:
        router.push("/");
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (!isMounted || typeof window === 'undefined') return;

    if (searchQuery.trim() !== "") {
      router.push(`/shop?search=${encodeURIComponent(searchQuery)}`);
      setShowSearch(false);
      setIsMenuOpen(false);
    }
  };

  return (
    <>
      {/* Updated overlay to match Hero component exactly */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="menu-overlay fixed inset-0 bg-black/50 backdrop-blur-sm z-[90]"
            onClick={() => setIsMenuOpen(false)}
          />
        )}
      </AnimatePresence>

      <nav
        className={`fixed top-0 w-full z-[9999] bg-gradient-to-l from-[#87878780] to-transparent backdrop-blur-[15px] text-white p-6 sm:p-8 transition-transform duration-300 border-b border-black/10 ${isVisible ? "translate-y-0" : "-translate-y-full"}`}
      >
        <div className="container mx-auto relative">
          {/* Main nav content */}
          <div className={`flex justify-between items-center relative z-[200] ${isMenuOpen ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}>
            {/* Logo and brand name with higher z-index */}
            <Link href={"/"} className="relative z-[60] flex items-center gap-3">
              <div className="bg-transparent p-1 rounded-full">
                <Image src="/logo.png" alt="Logo" width={40} height={50} />
              </div>
              <div className="block">
                <motion.span
                  className="text-base font-bold bg-clip-text text-transparent inline-block relative"
                  style={{
                    backgroundImage: "linear-gradient(90deg, #1E90FF, #FF69B4)",
                    backgroundSize: "200% 100%",
                  }}
                  animate={{
                    backgroundPosition: ["0% 0%", "100% 0%", "0% 0%"],
                  }}
                  transition={{
                    duration: 5,
                    ease: "linear",
                    repeat: Infinity,
                  }}
                >
                  CEPOKA BEAUTY HUB
                  <motion.div
                    className="absolute -bottom-1 left-0 h-[2px] rounded-full"
                    style={{
                      backgroundImage: "linear-gradient(90deg, #1E90FF, #FF69B4)",
                      backgroundSize: "200% 100%",
                    }}
                    animate={{
                      backgroundPosition: ["0% 0%", "100% 0%", "0% 0%"],
                      width: ["0%", "100%"],
                    }}
                    transition={{
                      backgroundPosition: {
                        duration: 5,
                        ease: "linear",
                        repeat: Infinity,
                      },
                      width: {
                        duration: 1,
                        delay: 0.5,
                        ease: "easeOut",
                      },
                    }}
                  />
                </motion.span>
              </div>
            </Link>

            <div className="flex items-center space-x-8">
              {/* Nav items - adjusted spacing */}
              <motion.ul
                animate={{ opacity: showSearch ? 0 : 1 }}
                transition={{ duration: 0.2 }}
                className={`list-none hidden md:flex items-center space-x-8 font-medium ${showSearch ? 'invisible' : 'visible'}`}
              >
                {["Home", "Shop", "Contact", "About"].map((link) => (
                  <li
                    key={link}
                    className={`relative cursor-pointer flex flex-col items-center ${activeLink === link
                      ? "text-[#1E90FF]"
                      : "text-white/90 hover:text-white transition-colors duration-200"
                      }`}
                    onClick={() => handleNavClick(link)}
                  >
                    <div>{link}</div>
                    {activeLink === link && (
                      <div className="absolute top-8 flex space-x-1">
                        <motion.div
                          className="bg-[#1E90FF] w-[4px] h-[4px] rounded-full"
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ duration: 0.2 }}
                        />
                        <motion.div
                          className="bg-[#FF69B4] w-[4px] h-[4px] rounded-full"
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ duration: 0.2, delay: 0.1 }}
                        />
                        <motion.div
                          className="bg-[#1E90FF] w-[4px] h-[4px] rounded-full"
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ duration: 0.2, delay: 0.2 }}
                        />
                        <motion.div
                          className="bg-[#FF69B4] w-[4px] h-[4px] rounded-full"
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ duration: 0.2, delay: 0.3 }}
                        />
                      </div>
                    )}
                  </li>
                ))}
              </motion.ul>

              {/* Search container - adjusted sizing */}
              <div className="relative flex items-center z-[60]">
                <button
                  type="button"
                  className="rounded-full p-3 cursor-pointer hover:bg-white/10 hidden md:flex items-center justify-center relative z-[102]"
                  onClick={() => setShowSearch(!showSearch)}
                >
                  <Search className="w-4 h-4 text-white" />
                </button>

                {/* Inline search bar */}
                {showSearch && (
                  <motion.form
                    initial={{ opacity: 0, width: 0 }}
                    animate={{ opacity: 1, width: "240px" }}
                    exit={{ opacity: 0, width: 0 }}
                    onSubmit={handleSearch}
                    className="absolute right-12 top-1/2 -translate-y-1/2"
                  >
                    <div className="relative">
                      <input
                        type="text"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="w-full px-4 py-2 rounded-full bg-white/10 text-white font-light focus:outline-none text-left placeholder-white/60"
                        placeholder="Search products..."
                        autoFocus
                      />
                    </div>
                  </motion.form>
                )}
              </div>

              {/* Mobile menu button with higher z-index */}
              <div className="md:hidden flex items-center">
                {!isMenuOpen && (
                  <button
                    id="hamburger-button"
                    className="text-white focus:outline-none z-[60] pr-4" // Added padding-right
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsMenuOpen(true);
                    }}
                  >
                    <svg
                      className="w-6 h-6"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M4 6h16M4 12h16m-7 6h7"
                      />
                    </svg>
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Menu - Black background with blur */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            id="mobile-menu-container"
            initial={{ opacity: 0, y: "-100%" }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: "-100%", transition: { duration: 0.3 } }}
            transition={{ duration: 0.3 }}
            className={`fixed top-0 left-0 w-full bg-black/90 backdrop-blur-[8px] z-[9998] md:hidden h-full ${isMenuOpen ? 'open' : ''}`}
            onClick={(e) => {
              e.stopPropagation();
              setIsMenuOpen(false);
            }}
          >
            {/* Mobile Header */}
            <div className="h-[90px] relative">
              <div className="absolute -bottom-5 left-0 w-full h-[1px] bg-white/10 z-[160]"></div>
              <div className="container mx-auto relative">
                <div className="flex justify-between items-center relative z-[10] pt-6 px-4">
                  <Link href={"/"} className="relative z-[10] flex items-center gap-3" onClick={(e) => e.stopPropagation()}>
                    <div className="bg-black/30 p-2 rounded-full">
                      <Image src="/logo.png" alt="Logo" width={40} height={50} />
                    </div>
                    <div>
                      <motion.span
                        className="text-base font-bold bg-clip-text text-transparent inline-block relative"
                        style={{
                          backgroundImage: "linear-gradient(90deg, #1E90FF, #FF69B4)",
                          backgroundSize: "200% 100%",
                        }}
                        animate={{
                          backgroundPosition: ["0% 0%", "100% 0%", "0% 0%"],
                        }}
                        transition={{
                          duration: 5,
                          ease: "linear",
                          repeat: Infinity,
                        }}
                      >
                        CEPOKA BEAUTY HUB
                      </motion.span>
                    </div>
                  </Link>

                  <motion.button
                    className="text-white bg-black/30 rounded-full p-3 relative z-[170] cursor-pointer hover:text-gray-300 hover:scale-110 transition-all duration-200"
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsMenuOpen(false);
                    }}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    aria-label="Close menu"
                  >
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </motion.button>
                </div>
              </div>
            </div>

            {/* Mobile Menu Content */}
            <div
              className="pt-10 pb-8 px-6 flex flex-col items-center gap-8"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Mobile Search Form */}
              <motion.form
                id="mobile-search-form"
                variants={menuItemVariants}
                custom={0}
                initial="closed"
                animate="open"
                className="relative w-full max-w-[280px]"
                onSubmit={handleSearch}
                onClick={(e) => e.stopPropagation()}
              >
                <div className="relative">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full px-6 py-3 rounded-full bg-white/10 text-white font-medium focus:outline-none focus:ring-2 focus:ring-[#1E90FF] transition-all text-left placeholder-white/60 border border-white/20"
                    placeholder="Search products..."
                  />
                  <button
                    type="submit"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 p-2 bg-gradient-to-r from-[#1E90FF] to-[#FF69B4] rounded-full"
                  >
                    <Search className="w-4 h-4 text-white" />
                  </button>
                </div>
              </motion.form>

              {/* Navigation Links */}
              <div className="flex flex-col items-center gap-6 w-full mt-4">
                {["Home", "Shop", "Contact", "About"].map((link, i) => (
                  <motion.div
                    key={link}
                    variants={menuItemVariants}
                    custom={i + 1}
                    initial="closed"
                    animate="open"
                    className="relative w-full"
                  >
                    <motion.button
                      onClick={() => handleNavClick(link)}
                      className={`text-center w-full text-lg font-medium py-3 px-6 rounded-lg ${activeLink === link
                        ? "bg-gradient-to-r from-[#1E90FF]/20 to-[#FF69B4]/20 text-white border border-white/20"
                        : "text-white hover:bg-white/5"
                        }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      {link}
                      {activeLink === link && (
                        <span className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-[#1E90FF] to-[#FF69B4] rounded-r-full"></span>
                      )}
                    </motion.button>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default Nav;
